import { Types } from 'mongoose';
import Notification from '../models/Notification';
import User from '../models/User';
import Shop from '../models/Shop';
import Product from '../models/Product';
import Order from '../models/Order';
import { NotificationType } from '../interfaces/notification.interfaces';

// Helper function to create a notification
const createNotification = async (
  userId: Types.ObjectId,
  heading: string,
  description: string,
  notificationType: NotificationType,
  orderId?: Types.ObjectId,
  productId?: Types.ObjectId,
  categoryId?: Types.ObjectId,
  subcategoryId?: Types.ObjectId
) => {
  try {
    const notification = new Notification({
      userId,
      heading,
      description,
      notificationType,
      orderId,
      productId,
      categoryId,
      subcategoryId,
      isRead: false
    });

    await notification.save();
    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
};

// Notify brand owner when they add a new product
export const notifyProductCreated = async (productId: Types.ObjectId, brandOwnerId: Types.ObjectId, productName: string) => {
  try {
    const heading = 'New Product Added';
    const description = `Your product "${productName}" has been successfully added to your shop.`;
    
    await createNotification(
      brandOwnerId,
      heading,
      description,
      NotificationType.PRODUCT_CREATED,
      undefined,
      productId
    );
  } catch (error) {
    console.error('Error notifying product created:', error);
  }
};

// Notify when order status changes (both brand owner and user)
export const notifyOrderStatusChanged = async (
  orderId: Types.ObjectId,
  orderNumber: string,
  newStatus: string,
  userId: Types.ObjectId,
  brandOwnerIds: Types.ObjectId[]
) => {
  try {
    // Notify the user who placed the order
    const userHeading = 'Order Status Updated';
    const userDescription = `Your order #${orderNumber} status has been updated to: ${newStatus}`;
    
    await createNotification(
      userId,
      userHeading,
      userDescription,
      NotificationType.ORDER_STATUS_CHANGED,
      orderId
    );

    // Notify all brand owners whose products are in the order
    const brandOwnerHeading = 'Order Status Updated';
    const brandOwnerDescription = `Order #${orderNumber} status has been updated to: ${newStatus}`;
    
    for (const brandOwnerId of brandOwnerIds) {
      await createNotification(
        brandOwnerId,
        brandOwnerHeading,
        brandOwnerDescription,
        NotificationType.ORDER_STATUS_CHANGED,
        orderId
      );
    }
  } catch (error) {
    console.error('Error notifying order status changed:', error);
  }
};

// Notify when a new order is created (both user and brand owners)
export const notifyOrderCreated = async (
  orderId: Types.ObjectId,
  orderNumber: string,
  userId: Types.ObjectId,
  brandOwnerIds: Types.ObjectId[]
) => {
  try {
    // Notify the user who placed the order
    const userHeading = 'Order Placed Successfully';
    const userDescription = `Your order #${orderNumber} has been placed successfully and is being processed.`;
    
    await createNotification(
      userId,
      userHeading,
      userDescription,
      NotificationType.ORDER_CREATED,
      orderId
    );

    // Notify all brand owners whose products are in the order
    const brandOwnerHeading = 'New Order Received';
    const brandOwnerDescription = `You have received a new order #${orderNumber} for your products.`;
    
    for (const brandOwnerId of brandOwnerIds) {
      await createNotification(
        brandOwnerId,
        brandOwnerHeading,
        brandOwnerDescription,
        NotificationType.ORDER_CREATED,
        orderId
      );
    }
  } catch (error) {
    console.error('Error notifying order created:', error);
  }
};

// Notify all brand owners when a new category is added by admin
export const notifyCategoryCreated = async (categoryId: Types.ObjectId, categoryName: string) => {
  try {
    // Get all brand owners
    const brandOwners = await User.find({ role: 'brandOwner', isBrandOwnerVerified: true });
    
    const heading = 'New Category Added';
    const description = `A new category "${categoryName}" has been added. You can now add products to this category.`;
    
    for (const brandOwner of brandOwners) {
      await createNotification(
        brandOwner._id,
        heading,
        description,
        NotificationType.CATEGORY_CREATED,
        undefined,
        undefined,
        categoryId
      );
    }
  } catch (error) {
    console.error('Error notifying category created:', error);
  }
};

// Notify all brand owners when a new subcategory is added by admin
export const notifySubcategoryCreated = async (subcategoryId: Types.ObjectId, subcategoryName: string, categoryName: string) => {
  try {
    // Get all brand owners
    const brandOwners = await User.find({ role: 'brandOwner', isBrandOwnerVerified: true });
    
    const heading = 'New Subcategory Added';
    const description = `A new subcategory "${subcategoryName}" has been added under "${categoryName}". You can now add products to this subcategory.`;
    
    for (const brandOwner of brandOwners) {
      await createNotification(
        brandOwner._id,
        heading,
        description,
        NotificationType.SUBCATEGORY_CREATED,
        undefined,
        undefined,
        undefined,
        subcategoryId
      );
    }
  } catch (error) {
    console.error('Error notifying subcategory created:', error);
  }
};

// Notify brand owner when product stock is low
export const notifyLowStock = async (productId: Types.ObjectId, productName: string, currentStock: number, brandOwnerId: Types.ObjectId) => {
  try {
    const heading = 'Low Stock Alert';
    const description = `Your product "${productName}" is running low on stock. Current stock: ${currentStock} units. Please restock soon.`;
    
    await createNotification(
      brandOwnerId,
      heading,
      description,
      NotificationType.LOW_STOCK,
      undefined,
      productId
    );
  } catch (error) {
    console.error('Error notifying low stock:', error);
  }
};

// Helper function to get brand owner IDs from an order
export const getBrandOwnerIdsFromOrder = async (orderId: Types.ObjectId): Promise<Types.ObjectId[]> => {
  try {
    const order = await Order.findById(orderId).populate('items.product');
    if (!order) return [];

    const shopIds = new Set<string>();
    
    // Get unique shop IDs from order items
    for (const item of order.items) {
      const product = item.product as any;
      if (product && product.shop) {
        shopIds.add(product.shop.toString());
      }
    }

    // Get brand owner IDs from shops
    const shops = await Shop.find({ _id: { $in: Array.from(shopIds) } });
    return shops.map(shop => shop.userId);
  } catch (error) {
    console.error('Error getting brand owner IDs from order:', error);
    return [];
  }
};
