export interface ICustomization {
  customWriting?: {
    text: string;
    alignment: 'left' | 'center' | 'right';
    characterLimit: number;
  };
  style: string; // Style ID or name
  color: string; // Color code or name
  size: string; // Size code
}

export interface ICustomProduct {
  _id?: string;
  baseProductId: string;
  userId: string;
  customization: ICustomization;
  price: number;
  quantity: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ICustomProductCreate {
  baseProductId: string;
  customization: ICustomization;
  quantity: number;
}

export interface ICustomProductUpdate {
  customization?: Partial<ICustomization>;
  quantity?: number;
}
