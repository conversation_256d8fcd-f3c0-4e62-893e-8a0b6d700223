import { body, validationResult } from 'express-validator';
import { StatusCodes } from 'http-status-codes';
import { Request, Response } from 'express';

export const adminLoginValidation = [
  // Validate email
  body('email').notEmpty().withMessage('Email is required').isEmail().withMessage('Invalid email format'),

  // Validate password
  body('password').notEmpty().withMessage('Password is required'),

  // Custom validation for checking errors
  (req: Request, res: Response, next: Function) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(StatusCodes.BAD_REQUEST).json({ errors: errors.array() });
    }
    next();
  }
];



export const adminSignupValidation = [
  // Validate email
  body('email').notEmpty().withMessage('Email is required').isEmail().withMessage('Invalid email format'),

  // Validate fullname
  body('fullname').notEmpty().withMessage('Full name is required'),

  // Validate phone number
  body('phoneNumber').notEmpty().withMessage('Phone number is required'),

  // Validate password
  body('password').notEmpty().withMessage('Password is required'),

  // Validate confirm password
  body('confirmPassword').notEmpty().withMessage('Confirm password is required'),

  // Validate role
  body('role').notEmpty().withMessage('Role is required').isIn(['admin']).withMessage('Invalid role. Only admin role is allowed'),

  // Custom password match validation
  (req: Request, res: Response, next: Function) => {
    if (req.body.password !== req.body.confirmPassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Passwords do not match' });
    }
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(StatusCodes.BAD_REQUEST).json({ errors: errors.array() });
    }
    next();
  }
];
