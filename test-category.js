const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

let data = new FormData();
data.append('categoryName', 'Test Category');
data.append('categoryDescription', 'Test category description');
data.append('categoryType', 'clothing');
data.append('files', fs.createReadStream('C:/Users/<USER>/OneDrive/Pictures/2918041.jpg'));

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'https://oyrq-backend-production.up.railway.app/api/categories/',
  headers: {
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2OGQxOTE1NjM1OGJkNTFkYzkyMTg4MDQiLCJyb2xlIjoiYWRtaW4iLCJpYXQiOjE3NTg1NzI3MjgsImV4cCI6MTc2MTE2NDcyOH0.051tKU1ml9523xr-saX1WKJLWUs_-7km_CAkojG-NhM',
    ...data.getHeaders()
  },
  data: data
};

axios.request(config)
.then((response) => {
  console.log(JSON.stringify(response.data));
})
.catch((error) => {
  console.log('Status:', error.response?.status);
  console.log('Data:', JSON.stringify(error.response?.data, null, 2));
});