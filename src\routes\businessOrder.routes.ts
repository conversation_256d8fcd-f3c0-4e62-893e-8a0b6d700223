import { Router } from 'express';
import { getShopOrders, getOrderById, updateOrderStatus, getOrderStats, 
    getOrdersPerMonth, getTotalRevenue, getRevenuePerMonth, getTopSellingProducts } from '../controllers/index';
import { verifyTokenForbrandOwner } from '../middlewares/auth.middlewares';

const router = Router();

// Apply business owner authentication to all routes
router.use(verifyTokenForbrandOwner);

// Get order statistics for dashboard
router.get('/stats', getOrderStats);

// Get top selling products
router.get('/top-selling-products', getTopSellingProducts);

// Analytics routes
router.get('/analytics/orders-per-month', getOrdersPerMonth);
router.get('/analytics/total-revenue', getTotalRevenue);
router.get('/analytics/revenue-per-month', getRevenuePerMonth);

// Get all orders related to business owner's shops
router.get('/', getShopOrders);

// Get single order details
router.get('/:orderId', getOrderById);

// Update order status
router.put('/:orderId/status', updateOrderStatus);

export default router;
