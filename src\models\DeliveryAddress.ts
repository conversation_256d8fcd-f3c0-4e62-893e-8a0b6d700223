import mongoose, { Schema } from 'mongoose';
import { IDeliveryAddress } from '../interfaces/user.interfaces';

const DeliveryAddressSchema = new Schema<IDeliveryAddress>(
  {
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    state: { 
      type: String, 
      required: true, 
      trim: true 
    },
    city: { 
      type: String, 
      required: true, 
      trim: true 
    },
    streetAddress: { 
      type: String, 
      required: true, 
      trim: true 
    },
    apartment: { 
      type: String, 
      trim: false 
    },
    postalCode: { 
      type: String, 
      required: true, 
      trim: true 
    },
    isDefault: { 
      type: Boolean, 
      default: false 
    }
  },
  { 
    timestamps: true 
  }
);

// Index for efficient queries
DeliveryAddressSchema.index({ user: 1, isDefault: 1 });

// Ensure only one default address per user
DeliveryAddressSchema.pre('save', async function(next) {
  if (this.isDefault) {
    // Remove default flag from other addresses of the same user
    await (this.constructor as any).updateMany(
      { user: this.user, _id: { $ne: this._id } },
      { isDefault: false }
    );
  }
  next();
});

const DeliveryAddress = mongoose.model<IDeliveryAddress>('DeliveryAddress', DeliveryAddressSchema);

export default DeliveryAddress;
