import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import User from '../models/User';
import { uploadFile } from '../utils/mediaHandling';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
// import { sendOtpEmail } from './helpers/OTP';

const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET) {
  throw new Error('JWT_SECRET is not defined in the environment variables');
}

// Sign up brand owner
export const signUpBrandOwner = async (req: Request, res: Response) => {
  try {
    const {
      role,
      brandName,
      email,
      phone,
      address,
      websitelink,
      bio,
      password,
      confirmPassword
    } = req.body;
    const files = req.files as Express.Multer.File[];

    // Validate required fields
    if (!role || !brandName || !email || !phone || !address || !password || !confirmPassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'All required fields must be provided'
      });
    }

    // Validate role
    if (role !== 'brandOwner') {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid role for brand owner signup'
      });
    }

    // Validate passwords match
    if (password !== confirmPassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Passwords do not match'
      });
    }

    // Validate password length
    if (password.length < 6) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Password must be at least 6 characters long'
      });
    }

    // Validate email format
    const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
    if (!emailRegex.test(email)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Please enter a valid email address'
      });
    }

    // Validate phone number
    const phoneRegex = /^\+?[\d\s-()]+$/;
    if (!phoneRegex.test(phone)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Please enter a valid phone number'
      });
    }

    // Validate website link if provided
    if (websitelink) {
      const websiteRegex = /^https?:\/\/.+/;
      if (!websiteRegex.test(websitelink)) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: 'Please enter a valid website URL'
        });
      }
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return res.status(StatusCodes.CONFLICT).json({
        success: false,
        message: 'User with this email already exists'
      });
    }

    // Handle profile picture upload
    let profilePictureUrl = "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS2TgOv9CMmsUzYKCcLGWPvqcpUk6HXp2mnww&s";
    if (files && files.length > 0) {
      try {
        profilePictureUrl = await uploadFile(files[0]);
      } catch (error) {
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          success: false,
          message: 'Error uploading profile picture'
        });
      }
    }

    // Create new brand owner
    const newBrandOwner = new User({
      email: email.toLowerCase(),
      fullname: brandName, // Using brandName as fullname for brand owners
      phoneNumber: phone,
      password,
      ProfilePicture: profilePictureUrl,
      brandName,
      address,
      websitelink,
      bio,
      role,
      isEmailVerified: true,
      isBrandOwnerVerified: false,
      isBrandOwner: true
    });

    await newBrandOwner.save();

    // // Send OTP for email verification
    // try {
    //   await sendOtpEmail(newBrandOwner.email, newBrandOwner.otp);
    // } catch (error) {
    //   console.error('Error sending OTP email:', error);
    // }

    return res.status(StatusCodes.CREATED).json({
      success: true,
      message: 'Brand owner registered successfully. Please wait for admin approval.',
      user: {
        id: newBrandOwner._id,
        email: newBrandOwner.email,
        brandName: newBrandOwner.brandName,
        role: newBrandOwner.role,
        isEmailVerified: true,
        isBrandOwnerVerified: newBrandOwner.isBrandOwnerVerified,
        ProfilePicture: newBrandOwner.ProfilePicture,
        isBrandOwner: newBrandOwner.isBrandOwner
      }
    });

  } catch (error) {
    console.error('Error in brand owner signup:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error creating brand owner account',
      error
    });
  }
};

// Update brand owner info (brandName, address, websitelink, bio, profilePicture)
export const updateBrandOwnerInfo = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const { brandName, phoneNumber, address, websitelink, bio } = req.body;
    const files = req.files as Express.Multer.File[];

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Brand owner not found'
      });
    }

    if (user.role !== 'brandOwner') {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'Access denied. Only business owners can update brand info'
      });
    }

    // Prepare update data
    const updateData: any = {};

    if (brandName) {
      if (brandName.trim().length === 0 || brandName.length > 100) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: 'Brand name must be between 1 and 100 characters'
        });
      }
      updateData.brandName = brandName.trim();
      updateData.fullname = brandName.trim(); // Update fullname as well
    }

    if (phoneNumber) {
      console.log("phone number is" , phoneNumber);
      const phoneRegex = /^\+?[\d\s-()]+$/;
      if (!phoneRegex.test(phoneNumber)) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: 'Please enter a valid phone number'
        });
      }
      updateData.phoneNumber = phoneNumber.trim();
    }

    if (address) {
      if (address.length > 500) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: 'Address cannot exceed 500 characters'
        });
      }
      updateData.address = address.trim();
    }

    if (websitelink) {
      const websiteRegex = /^https?:\/\/.+/;
      if (!websiteRegex.test(websitelink)) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: 'Please enter a valid website URL'
        });
      }
      updateData.websitelink = websitelink.trim();
    }

    if (bio) {
      if (bio.length > 1000) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: 'Bio cannot exceed 1000 characters'
        });
      }
      updateData.bio = bio.trim();
    }

    // Handle profile picture upload
    if (files && files.length > 0) {
      try {
        const profilePictureUrl = await uploadFile(files[0]);
        updateData.ProfilePicture = profilePictureUrl;
      } catch (error) {
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          success: false,
          message: 'Error uploading profile picture'
        });
      }
    }

    // Update brand owner
    const updatedBrandOwner = await User.findByIdAndUpdate(
      userId,
      updateData,
      { new: true, runValidators: true }
    ).select('-password -otp -otpCreatedAt');

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Brand owner information updated successfully',
      brandOwner: {
        id: updatedBrandOwner?._id,
        brandName: updatedBrandOwner?.brandName,
        address: updatedBrandOwner?.address,
        websitelink: updatedBrandOwner?.websitelink,
        bio: updatedBrandOwner?.bio,
        ProfilePicture: updatedBrandOwner?.ProfilePicture,
        email: updatedBrandOwner?.email,
        phoneNumber: updatedBrandOwner?.phoneNumber,
        role: updatedBrandOwner?.role
      }
    });

  } catch (error) {
    console.error('Error updating brand owner info:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error updating brand owner information',
      error
    });
  }
};

// Update brand owner password
export const updateBrandOwnerPassword = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const { oldPassword, newPassword, confirmPassword } = req.body;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    // Validate required fields
    if (!oldPassword || !newPassword || !confirmPassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Old password, new password, and confirm password are required'
      });
    }

    // Check if new password matches confirm password
    if (newPassword !== confirmPassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'New password and confirm password do not match'
      });
    }

    // Validate new password length
    if (newPassword.length < 6) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'New password must be at least 6 characters long'
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Brand owner not found'
      });
    }

    if (user.role !== 'brandOwner') {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'Access denied. Only business owners can update password'
      });
    }

    // Verify old password
    const isOldPasswordValid = await user.comparePassword(oldPassword);
    if (!isOldPasswordValid) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'Old password is incorrect'
      });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(12);
    const hashedNewPassword = await bcrypt.hash(newPassword, salt);

    // Update password
    await User.findByIdAndUpdate(userId, { password: hashedNewPassword });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Password updated successfully'
    });

  } catch (error) {
    console.error('Error updating brand owner password:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error updating password',
      error
    });
  }
};

// Delete brand
export const deleteBrand = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Brand owner not found'
      });
    }

    if (user.role !== 'brandOwner') {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'Access denied. Only business owners can delete their brand'
      });
    }
    

    // Delete the brand owner account
    await User.findByIdAndDelete(userId);

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Brand deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting brand:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error deleting brand',
      error
    });
  }
};

// Get brand info
export const getBrandInfo = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const brandOwner = await User.findById(userId).select('brandName address websitelink bio ProfilePicture email phoneNumber role');
    if (!brandOwner) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Brand owner not found'
      });
    }

    if (brandOwner.role !== 'brandOwner') {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'Access denied. Only business owners can access brand info'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Brand information retrieved successfully',
      brand: {
        id: brandOwner._id,
        brandName: brandOwner.brandName,
        address: brandOwner.address,
        websitelink: brandOwner.websitelink,
        bio: brandOwner.bio,
        ProfilePicture: brandOwner.ProfilePicture,
        email: brandOwner.email,
        phoneNumber: brandOwner.phoneNumber
      }
    });

  } catch (error) {
    console.error('Error getting brand info:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving brand information',
      error
    });
  }
};