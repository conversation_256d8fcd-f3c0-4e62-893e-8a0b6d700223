import { Document, Types } from 'mongoose';

export interface INotification extends Document {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  heading: string;
  description: string;
  isRead: boolean;
  orderId?: Types.ObjectId; // Optional field for order-related notifications
  productId?: Types.ObjectId; // Optional field for product-related notifications
  categoryId?: Types.ObjectId; // Optional field for category-related notifications
  subcategoryId?: Types.ObjectId; // Optional field for subcategory-related notifications
  notificationType: NotificationType;
  createdAt: Date;
  updatedAt: Date;
}

export enum NotificationType {
  PRODUCT_CREATED = 'PRODUCT_CREATED',
  ORDER_STATUS_CHANGED = 'ORDER_STATUS_CHANGED',
  ORDER_CREATED = 'ORDER_CREATED',
  CATEGORY_CREATED = 'CATEGORY_CREATED',
  SUBCATEGORY_CREATED = 'SUBCATEGORY_CREATED',
  LOW_STOCK = 'LOW_STOCK'
}

export interface ICreateNotificationRequest {
  userId: string;
  heading: string;
  description: string;
  orderId?: string;
  productId?: string;
  categoryId?: string;
  subcategoryId?: string;
  notificationType: NotificationType;
}

export interface INotificationResponse {
  id: string;
  userId: string;
  heading: string;
  description: string;
  isRead: boolean;
  orderId?: string;
  productId?: string;
  categoryId?: string;
  subcategoryId?: string;
  notificationType: NotificationType;
  createdAt: Date;
  updatedAt: Date;
}

export interface IGetNotificationsQuery {
  page?: number;
  limit?: number;
  isRead?: boolean;
  notificationType?: NotificationType;
}
