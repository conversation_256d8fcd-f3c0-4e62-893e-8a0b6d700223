import express from 'express';
import { CustomProductController } from '../controllers/customProduct.controller';
import { verifyToken } from '../middlewares/auth.middlewares';

const router = express.Router();

// All routes require authentication
router.use(verifyToken);

// Create custom product
router.post('/', CustomProductController.createCustomProduct);

// Get user's custom products
router.get('/', CustomProductController.getUserCustomProducts);

// Get customization options for a product
router.get('/options/:productId', CustomProductController.getCustomizationOptions);

// Get single custom product
router.get('/:id', CustomProductController.getCustomProduct);

// Update custom product
router.put('/:id', CustomProductController.updateCustomProduct);

// Delete custom product
router.delete('/:id', CustomProductController.deleteCustomProduct);

export default router;
