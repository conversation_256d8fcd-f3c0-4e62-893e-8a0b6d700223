import { Request, Response, NextFunction } from "express";
import multer, { MulterError } from "multer";

// Configure multer for multiple file uploads (images only)
const upload = multer({ 
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50 MB per file
    files: 5 // Maximum 5 files per upload
  },
  fileFilter: (req, file, cb) => {
    // Accept images only
    if (file.mimetype.startsWith("image/")) {
      cb(null, true);
    } else {
      cb(new Error("Only image files are allowed (jpg, png, gif, etc.)"));
    }
  }
}).array("files", 1); 

export const validateMedia = (req: Request, res: Response, next: NextFunction) => {
  upload(req, res, (error: any) => {
    if (error) {
      console.error("Multer Error:", error);
      if (error instanceof MulterError) {
        if (error.code === "LIMIT_FILE_SIZE") {
          res.status(400).json({ message: "File size too large. Maximum 10MB per file." });
        } else if (error.code === "LIMIT_FILE_COUNT") {
          res.status(400).json({ message: "Too many files. Maximum 50 files allowed." });
        } else {
          res.status(400).json({ message: `Upload Error: fiel${error.message}` });
        }
      } else {
        res.status(400).json({ message: `Upload Error: ${error.message}` });
      }
      req.destroy();
      return;
    }
    next();
  });
};
