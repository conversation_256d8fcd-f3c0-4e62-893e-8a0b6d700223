import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { Types } from 'mongoose';
import User from '../models/User';
import { uploadFile } from '../utils/mediaHandling';
import bcrypt from 'bcryptjs';

// Update user info (fullname, phoneNumber, password, profilePicture)
export const updateUserInfo = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const { fullname, phoneNumber, countryCode } = req.body;
    const files = req.files as Express.Multer.File[];

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'User not found'
      });
    }

    // Prepare update data
    const updateData: any = {};

    if (fullname) {
      if (fullname.trim().length === 0 || fullname.length > 100) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: 'Full name must be between 1 and 100 characters'
        });
      }
      updateData.fullname = fullname.trim();
    }

    if (phoneNumber) {
      const phoneRegex = /^\+?[\d\s-()]+$/;
      if (!phoneRegex.test(phoneNumber)) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: 'Please enter a valid phone number'
        });
      }
      updateData.phoneNumber = phoneNumber.trim();
    }

    if(countryCode){
      updateData.countryCode = countryCode.trim();
    }

    // Handle profile picture upload
    if (files && files.length > 0) {
      try {
        const profilePictureUrl = await uploadFile(files[0]);
        updateData.ProfilePicture = profilePictureUrl;
      } catch (error) {
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          success: false,
          message: 'Error uploading profile picture'
        });
      }
    }

    // Update user
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      updateData,
      { new: true, runValidators: true }
    ).select('-password -otp -otpCreatedAt');

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'User information updated successfully',
      user: {
        id: updatedUser?._id,
        fullname: updatedUser?.fullname,
        email: updatedUser?.email,
        phoneNumber: updatedUser?.phoneNumber,
        ProfilePicture: updatedUser?.ProfilePicture,
        role: updatedUser?.role
      }
    });

  } catch (error) {
    console.error('Error updating user info:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error updating user information',
      error
    });
  }
};

// Delete user profile
export const deleteUserProfile = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'User not found'
      });
    }

    // Delete the user
    await User.findByIdAndDelete(userId);

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'User profile deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting user profile:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error deleting user profile',
      error
    });
  }
};

// Get user info (fullname, phoneNumber, profilePicture, email, address)
export const getUserInfo = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const user = await User.findById(userId).select('fullname phoneNumber ProfilePicture email address');
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'User not found'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'User information retrieved successfully',
      user: {
        id: user._id,
        fullname: user.fullname,
        email: user.email,
        phoneNumber: user.phoneNumber,
        countryCode: user.countryCode,
        ProfilePicture: user.ProfilePicture,
        address: user.address
      }
    });

  } catch (error) {
    console.error('Error getting user info:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving user information',
      error
    });
  }
};

// Update password
export const updatePassword = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const { oldPassword, newPassword, confirmPassword } = req.body;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    // Validate required fields
    if (!oldPassword || !newPassword || !confirmPassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Old password, new password, and confirm password are required'
      });
    }

    // Check if new password matches confirm password
    if (newPassword !== confirmPassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'New password and confirm password do not match'
      });
    }

    // Validate new password length
    if (newPassword.length < 6) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'New password must be at least 6 characters long'
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'User not found'
      });
    }

    // Verify old password
    const isOldPasswordValid = await user.comparePassword(oldPassword);
    if (!isOldPasswordValid) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'Old password is incorrect'
      });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(12);
    const hashedNewPassword = await bcrypt.hash(newPassword, salt);

    // Update password
    await User.findByIdAndUpdate(userId, { password: hashedNewPassword });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Password updated successfully'
    });

  } catch (error) {
    console.error('Error updating password:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error updating password',
      error
    });
  }
};