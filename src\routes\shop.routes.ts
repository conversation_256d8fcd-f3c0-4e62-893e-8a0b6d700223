import { Router } from 'express';
import { 
  createShop, 
  updateShop, 
  getUserShops, 
  getShopDetails, 
  deleteShop,
  getAllShops
} from '../controllers/shop.controller';
import { verifyTokenForbrandOwner } from '../middlewares/auth.middlewares';
import { validateMedia } from '../utils/validateMedia';

const router = Router();

// Public route - Get all shops with owner details
router.get('/public', getAllShops);

// All other shop routes require business owner authentication
router.use(verifyTokenForbrandOwner);

// Create a new shop
router.post('/',validateMedia, createShop);

// Get all shops of the authenticated user
router.get('/', getUserShops);

// Get specific shop details
router.get('/:shopId', getShopDetails);

// Update shop information
router.put('/:shopId', validateMedia,updateShop);

// Delete shop
router.delete('/:shopId', deleteShop);

export default router;
