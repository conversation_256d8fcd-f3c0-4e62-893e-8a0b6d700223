import { Router } from 'express';
import { getUserOrders, getOrder, getOrderByNumber, cancelOrder } from '../controllers/index';
import { verifyTokenForUser } from '../middlewares/auth.middlewares';

const router = Router();

// Apply user authentication to all order routes
router.use(verifyTokenForUser);

// Get user's orders with pagination
router.get('/', getUserOrders);

// Get single order by ID
router.get('/:orderId', getOrder);

// Get order by order number
router.get('/number/:orderNumber', getOrderByNumber);

// Cancel order
router.patch('/:orderId/cancel', cancelOrder);

export default router;