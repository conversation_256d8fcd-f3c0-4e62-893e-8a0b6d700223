import { Document, Types } from 'mongoose';
import { IUser } from './user.interfaces';

export interface IShop extends Document {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  shopName: string;
  shopImage: string;
  noOfProducts: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface IShopPopulated extends Omit<IShop, 'userId'> {
  userId: IUser;
}

export interface ICreateShopRequest {
  shopName: string;
}

export interface IUpdateShopRequest {
  shopName?: string;
}
