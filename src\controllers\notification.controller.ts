import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { Types } from 'mongoose';
import Notification from '../models/Notification';
import { IGetNotificationsQuery, INotificationResponse, NotificationType } from '../interfaces/notification.interfaces';

// Get all notifications for a user (with pagination and filtering)
export const getAllNotifications = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    
    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const {
      page = 1,
      limit = 20,
      isRead,
      notificationType
    }: IGetNotificationsQuery = req.query;

    const pageNum = parseInt(page.toString());
    const limitNum = parseInt(limit.toString());
    const skip = (pageNum - 1) * limitNum;

    // Build filter query
    const filter: any = { userId: new Types.ObjectId(userId) };
    
    if (isRead !== undefined) {
      filter.isRead = isRead === true;
    }
    
    if (notificationType) {
      filter.notificationType = notificationType;
    }

    // Get notifications with pagination
    const notifications = await Notification.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum)
      .populate('orderId', 'orderNumber orderStatus')
      .populate('productId', 'productName')
      .populate('categoryId', 'categoryName')
      .populate('subcategoryId', 'subCategoryName');

    // Get total count for pagination
    const totalCount = await Notification.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / limitNum);
    const unreadCount = await Notification.countDocuments({ 
      userId: new Types.ObjectId(userId), 
      isRead: false 
    });

    const notificationResponses: INotificationResponse[] = notifications.map(notification => ({
      id: notification._id.toString(),
      userId: notification.userId.toString(),
      heading: notification.heading,
      description: notification.description,
      isRead: notification.isRead,
      orderId: notification.orderId?.toString(),
      productId: notification.productId?.toString(),
      categoryId: notification.categoryId?.toString(),
      subcategoryId: notification.subcategoryId?.toString(),
      notificationType: notification.notificationType,
      createdAt: notification.createdAt,
      updatedAt: notification.updatedAt
    }));

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Notifications retrieved successfully',
      data: {
        notifications: notificationResponses,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalCount,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        },
        unreadCount
      }
    });

  } catch (error) {
    console.error('Error getting notifications:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving notifications',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Mark a specific notification as read
export const readNotification = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const { notificationId } = req.params;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    if (!Types.ObjectId.isValid(notificationId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid notification ID'
      });
    }

    const notification = await Notification.findOneAndUpdate(
      { 
        _id: new Types.ObjectId(notificationId), 
        userId: new Types.ObjectId(userId) 
      },
      { isRead: true },
      { new: true }
    );

    if (!notification) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Notification not found'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Notification marked as read',
      data: {
        id: notification._id.toString(),
        isRead: notification.isRead
      }
    });

  } catch (error) {
    console.error('Error marking notification as read:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error marking notification as read',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Mark all notifications as read for a user
export const readAllNotifications = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const result = await Notification.updateMany(
      {
        userId: new Types.ObjectId(userId),
        isRead: false
      },
      { isRead: true }
    );

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'All notifications marked as read',
      data: {
        modifiedCount: result.modifiedCount
      }
    });

  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error marking all notifications as read',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Delete a specific notification
export const deleteNotification = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const { notificationId } = req.params;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    if (!Types.ObjectId.isValid(notificationId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid notification ID'
      });
    }

    const notification = await Notification.findOneAndDelete({
      _id: new Types.ObjectId(notificationId),
      userId: new Types.ObjectId(userId)
    });

    if (!notification) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Notification not found'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Notification deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting notification:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error deleting notification',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Clear all notifications for a user
export const clearAllNotifications = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const result = await Notification.deleteMany({
      userId: new Types.ObjectId(userId)
    });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'All notifications cleared successfully',
      data: {
        deletedCount: result.deletedCount
      }
    });

  } catch (error) {
    console.error('Error clearing all notifications:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error clearing all notifications',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
