import { Document, Types } from 'mongoose';
import { TypesOfUsersEnum } from "../types/userTypes";

export interface IDeliveryAddress extends Document {
  _id: Types.ObjectId;
  user: Types.ObjectId; // Reference to User
  state: string;
  city: string;
  streetAddress: string;
  apartment?: string;
  postalCode: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface GooglePayload {
  email?: string;
  name?: string;
  picture?: string;
  sub: string;
}

export interface IUser extends Document {
  _id: Types.ObjectId;
  email: string;
  fullname: string;
  phoneNumber?: string; // Made optional for Google OAuth
  countryCode?: string;
  password?: string; // Made optional for Google OAuth
  ProfilePicture: string;
  brandName?: string;
  address?: string;
  websitelink?: string;
  bio?: string;
  role: TypesOfUsersEnum;
  isEmailVerified: boolean;
  isBrandOwnerVerified: boolean;
  isBrandOwner: boolean;
  otp: string;
  otpCreatedAt: Date;

  // Google OAuth specific fields
  googleId?: string;
  isGoogleUser: boolean;
  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
}