import { Document, Types } from 'mongoose';
import { ProductSize } from './product.interfaces';

export interface ICartItem {
  product: Types.ObjectId;
  quantity: number;
  size: ProductSize;
  color: string;
  priceAtTime: number; // Store price at time of adding to cart
}

export interface ICart extends Document {
  _id: Types.ObjectId;
  user: Types.ObjectId;
  items: ICartItem[];
  totalAmount: number;
  appliedPromoCode?: string; // Store the applied promo code
  discountAmount?: number; // Store the discount amount
  createdAt: Date;
  updatedAt: Date;
}

export interface IAddToCartRequest {
  productId: string;
  quantity: number;
  size: ProductSize;
  color: string;
}

export interface IUpdateCartItemRequest {
  quantity: number;
}

export interface IRemoveFromCartRequest {
  productId: string;
  size: ProductSize;
  color: string;
}
