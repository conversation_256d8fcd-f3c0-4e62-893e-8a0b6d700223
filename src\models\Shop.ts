import mongoose, { Schema } from 'mongoose';
import { IShop } from '../interfaces/shop.interfaces';

const ShopSchema: Schema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required'],
    index: true
  },
  shopName: {
    type: String,
    required: [true, 'Shop name is required'],
    unique: true,
    trim: true,
    maxlength: [100, 'Shop name cannot exceed 100 characters'],
    minlength: [2, 'Shop name must be at least 2 characters']
  },
  shopImage: {
    type: String,
    required: [false, 'Shop image is required'],
    default: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS2TgOv9CMmsUzYKCcLGWPvqcpUk6HXp2mnww&s'
  },
  noOfProducts: {
    type: Number,
    default: 0,
    min: [0, 'Number of products cannot be negative']
  }
}, {
  timestamps: true
});

// Index for efficient queries
ShopSchema.index({ userId: 1, shopName: 1 });

export default mongoose.model<IShop>('Shop', ShopSchema);
