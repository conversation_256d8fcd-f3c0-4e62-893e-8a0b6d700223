import mongoose, { Schema } from 'mongoose';
import { IProduct, ProductSize } from '../interfaces/product.interfaces';

const ProductSizes: ProductSize[] = ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'];

const ProductSchema: Schema = new Schema({
  productName: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    maxlength: [100, 'Product name cannot exceed 100 characters'],
  },
  productDescription: {
    type: String,
    required: [true, 'Product description is required'],
    trim: true,
    maxlength: [1000, 'Product description cannot exceed 1000 characters'],
  },
  productCategory: {
    type: Schema.Types.ObjectId,
    ref: 'Category',
    required: [true, 'Product category is required'],
    index: true
  },
  productSubCategory: {
    type: Schema.Types.ObjectId,
    ref: 'SubCategory',
    required: [true, 'Product subcategory is required'],
    index: true
  },
  shop: {
    type: Schema.Types.ObjectId,
    ref: 'Shop',
    required: [true, 'Shop is required'],
    index: true
  },
  tags: {
    type: [String],
    default: [],
    validate: {
      validator: function(tags: string[]) {
        return tags.length <= 10;
      },
      message: 'Maximum 10 tags allowed'
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  productMedia: {
    type: [String],
    required: [true, 'At least one product image is required'],
    validate: [
      {
        validator: function(media: string[]) {
          return media.length > 0 && media.length <= 10;
        },
        message: 'Product must have between 1 and 10 media files'
      },
      {
        validator: function(media: string[]) {
          return media.every(url => /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i.test(url));
        },
        message: 'All product media must be valid image URLs'
      }
    ]
  },
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: [0, 'Price cannot be negative'],
    validate: {
      validator: function(price: number) {
        return Number.isFinite(price) && price >= 0;
      },
      message: 'Price must be a valid positive number'
    }
  },
  discountedPercentage: {
    type: Number,
    min: [0, 'Discount percentage cannot be negative'],
    max: [100, 'Discount percentage cannot exceed 100']
  },
  discountedPrice: {
    type: Number,
    default: 0,
    min: [0, 'Discounted price cannot be negative']
  },
  sizes: {
    type: [String],
    enum: {
      values: ProductSizes,
      message: `Size must be one of: ${ProductSizes.join(', ')}`
    },
    default: [],
    validate: {
      validator: function(sizes: string[]) {
        return sizes.length <= ProductSizes.length;
      },
      message: 'Invalid sizes provided'
    }
  },
  colors: {
    type: [String],
    default: [],
    validate: {
      validator: function(colors: string[]) {
        return colors.length <= 20;
      },
      message: 'Maximum 20 colors allowed'
    }
  },
  stockQuantity: {
    type: Number,
    required: [true, 'Stock quantity is required'],
    min: [0, 'Stock quantity cannot be negative'],
    default: 0
  },
  stockSold: {
    type: Number,
    default: 0,
    min: [0, 'Stock sold cannot be negative']
  },
  isLowStock: {
    type: Boolean,
    default: false
  },
  sizeChartImage: {
    type: String,
    required: false,
    default: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS2TgOv9CMmsUzYKCcLGWPvqcpUk6HXp2mnww&s'
  }
}, {
  timestamps: true
});

// Indexes for efficient queries
ProductSchema.index({ shop: 1, isActive: 1 });
ProductSchema.index({ productCategory: 1, productSubCategory: 1 });
ProductSchema.index({ isLowStock: 1 });
ProductSchema.index({ productName: 'text', productDescription: 'text', tags: 'text' });

// Pre-save middleware to check low stock
ProductSchema.pre<IProduct>('save', function(next) {
  this.isLowStock = this.stockQuantity < 50;
  next();
});

export default mongoose.model<IProduct>('Product', ProductSchema);
