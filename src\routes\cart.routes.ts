import { Router } from 'express';
import { getCart, addToCart, updateCartItem, removeFromCart, 
    clearCart, getCartItemCount } from '../controllers/index';
import { verifyTokenForUser } from '../middlewares/auth.middlewares';

const router = Router();

// Apply user authentication to all cart routes
router.use(verifyTokenForUser);

// Get user's cart
router.get('/', getCart);

// Add item to cart
router.post('/add', addToCart);

// Update cart item quantity
router.put('/item/:productId/:size/:color', updateCartItem);

// Remove item from cart
router.delete('/item/:productId/:size/:color', removeFromCart);

// Clear entire cart
router.delete('/clear', clearCart);

// Get cart item count
router.get('/count', getCartItemCount);

export default router;
