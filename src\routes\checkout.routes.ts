import express from 'express';
import { applyPromoCode, removePromoCode, 
    getCheckoutSummary, processCheckout } from '../controllers/index';
import { verifyTokenForUser } from '../middlewares/auth.middlewares';

const router = express.Router();

// All routes require user authentication
router.use(verifyTokenForUser);

// Apply promo code
router.post('/promo-code', applyPromoCode);
router.delete('/promo-code', removePromoCode);

// Get checkout summary
router.get('/summary', getCheckoutSummary);

// Process checkout and create order
router.post('/process', processCheckout);

export default router;
