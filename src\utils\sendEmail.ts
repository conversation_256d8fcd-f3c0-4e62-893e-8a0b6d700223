import { StatusCodes } from "http-status-codes";
import nodemailer from "nodemailer";
import { Response } from "express";

export const accountMail = async (
  email: string,
  subject: string,
  text: string,
  username: string,
  res: Response
) => {
  const transport = nodemailer.createTransport({
    service: "gmail",
    host: "smtp.gmail.com",
    port: 465,
    auth: {
      user: "<EMAIL>",
      pass: "ewtdagfikqkbxhym"
    },
  });
  const mailOptions = {
    from: "<EMAIL>",
    to: email,
    subject,
    html: `
        <div style="font-family: Helvetica,Arial,sans-serif;min-width:600px;overflow:auto;line-height:2">
            <div style="margin:20px auto;width:70%;">
                <div style="border-bottom:1px solid #eee">
                <a href="" style="font-size:1.4em;color: #351C75;text-decoration:none;font-weight:600">OYRQ</a>
                </div>
                <p style="font-size:1.1em">Hi, ${username}</p>
                <p>Thank you for choosing OYRQ. Use the following OTP to Verify</p>
                <h2 style="background: #351C75;margin: 0 auto;width: max-content;padding: 0 10px;color: #fff;border-radius: 4px;">${text}</h2>
                <p style="font-size:0.9em;">Regards,<br />OYRQ</p>
                <hr style="border:none;border-top:1px solid #eee" />
                <div style="float:right;padding:8px 0;color:#aaa;font-size:0.8em;line-height:1;font-weight:300">
                <p>OYQR</p>
                <p>1600 Amphitheatre Parkway</p>
                <p>California</p>
                </div>
            </div>
        </div>
        `,
  };
  transport.sendMail(mailOptions, (err: any) => {
    if (err) {
      res.status(StatusCodes.CONFLICT).json({ message: "Email Not Sent" });
      return;
    }
    res.status(StatusCodes.OK).json({ message: "Email Sent" });
    return;
  });
};
