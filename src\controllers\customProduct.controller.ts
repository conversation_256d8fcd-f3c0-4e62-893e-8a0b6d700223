import { Request, Response } from 'express';
import CustomProduct from '../models/CustomProduct';
import Product from '../models/Product';
import { ICustomProductCreate, ICustomProductUpdate } from '../interfaces/customProduct.interfaces';

export class CustomProductController {
  // Create a custom product
  static async createCustomProduct(req: Request, res: Response) {
    try {
      const userId = req.user?._id;
      if (!userId) {
        return res.status(401).json({ 
          success: false, 
          message: 'Authentication required' 
        });
      }

      const { baseProductId, customization, quantity }: ICustomProductCreate = req.body;

      // Validate base product exists
      const baseProduct = await Product.findById(baseProductId);
      if (!baseProduct) {
        return res.status(404).json({
          success: false,
          message: 'Base product not found'
        });
      }

      // Validate customization data
      const validationError = CustomProductController.validateCustomization(customization);
      if (validationError) {
        return res.status(400).json({
          success: false,
          message: validationError
        });
      }

      // Calculate price (base product price + customization fee)
      const customizationFee = customization.customWriting?.text ? 5 : 0; // $5 for custom text
      const totalPrice = (baseProduct.price + customizationFee) * quantity;

      const customProduct = new CustomProduct({
        baseProductId,
        userId,
        customization,
        price: totalPrice,
        quantity
      });

      await customProduct.save();
      await customProduct.populate('baseProductId', 'name images category');

      res.status(201).json({
        success: true,
        message: 'Custom product created successfully',
        data: customProduct
      });

    } catch (error) {
      console.error('Create custom product error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get user's custom products
  static async getUserCustomProducts(req: Request, res: Response) {
    try {
      const userId = req.user?._id;
      const { page = 1, limit = 10 } = req.query;

      const skip = (Number(page) - 1) * Number(limit);

      const customProducts = await CustomProduct.find({ userId })
        .populate('baseProductId', 'name images category price')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Number(limit));

      const total = await CustomProduct.countDocuments({ userId });

      res.status(200).json({
        success: true,
        data: {
          customProducts,
          pagination: {
            currentPage: Number(page),
            totalPages: Math.ceil(total / Number(limit)),
            totalItems: total,
            itemsPerPage: Number(limit)
          }
        }
      });

    } catch (error) {
      console.error('Get user custom products error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get single custom product
  static async getCustomProduct(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user?._id;

      const customProduct = await CustomProduct.findOne({ 
        _id: id, 
        userId 
      }).populate('baseProductId', 'name images category price description');

      if (!customProduct) {
        return res.status(404).json({
          success: false,
          message: 'Custom product not found'
        });
      }

      res.status(200).json({
        success: true,
        data: customProduct
      });

    } catch (error) {
      console.error('Get custom product error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Update custom product
  static async updateCustomProduct(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user?._id;
      const updateData: ICustomProductUpdate = req.body;

      const customProduct = await CustomProduct.findOne({ 
        _id: id, 
        userId 
      }).populate('baseProductId');

      if (!customProduct) {
        return res.status(404).json({
          success: false,
          message: 'Custom product not found'
        });
      }

      // Validate customization if being updated
      if (updateData.customization) {
        // Deep merge customization objects
        const currentCustomization = customProduct.customization as any;
        const mergedCustomization: any = {
          style: updateData.customization.style || currentCustomization.style,
          color: updateData.customization.color || currentCustomization.color,
          size: updateData.customization.size || currentCustomization.size
        };
        
        // Handle customWriting deep merge
        if (currentCustomization.customWriting || updateData.customization.customWriting) {
          mergedCustomization.customWriting = {
            text: updateData.customization.customWriting?.text || currentCustomization.customWriting?.text,
            alignment: updateData.customization.customWriting?.alignment || currentCustomization.customWriting?.alignment || 'center',
            characterLimit: updateData.customization.customWriting?.characterLimit || currentCustomization.customWriting?.characterLimit || 50
          };
          
          // Remove customWriting if no text is provided
          if (!mergedCustomization.customWriting.text) {
            delete mergedCustomization.customWriting;
          }
        }
        
        const validationError = CustomProductController.validateCustomization(mergedCustomization);
        if (validationError) {
          return res.status(400).json({
            success: false,
            message: validationError
          });
        }

        customProduct.customization = mergedCustomization;
      }

      if (updateData.quantity) {
        customProduct.quantity = updateData.quantity;
      }

      // Recalculate price
      const baseProduct = customProduct.baseProductId as any;
      const customizationFee = customProduct.customization.customWriting?.text ? 5 : 0;
      customProduct.price = (baseProduct.price + customizationFee) * customProduct.quantity;

      await customProduct.save();

      res.status(200).json({
        success: true,
        message: 'Custom product updated successfully',
        data: customProduct
      });

    } catch (error) {
      console.error('Update custom product error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Delete custom product
  static async deleteCustomProduct(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user?._id;

      const customProduct = await CustomProduct.findOneAndDelete({ 
        _id: id, 
        userId 
      });

      if (!customProduct) {
        return res.status(404).json({
          success: false,
          message: 'Custom product not found'
        });
      }

      res.status(200).json({
        success: true,
        message: 'Custom product deleted successfully'
      });

    } catch (error) {
      console.error('Delete custom product error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get available customization options for a product
  static async getCustomizationOptions(req: Request, res: Response) {
    try {
      const { productId } = req.params;

      const product = await Product.findById(productId);
      if (!product) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }

      // Return available customization options
      const customizationOptions = {
        styles: [
          { id: 'hoodie', name: 'Hoodie', available: true },
          { id: 'sweater', name: 'Sweater', available: true },
          { id: 'jacket', name: 'Jacket', available: true },
          { id: 'vest', name: 'Vest', available: true }
        ],
        colors: [
          { code: '#000000', name: 'Black', available: true },
          { code: '#808080', name: 'Gray', available: true },
          { code: '#FF0000', name: 'Red', available: true },
          { code: '#FFA500', name: 'Orange', available: true },
          { code: '#FFFF00', name: 'Yellow', available: true },
          { code: '#000080', name: 'Navy', available: true }
        ],
        sizes: [
          { code: 'XS', name: 'Extra Small', available: true },
          { code: 'S', name: 'Small', available: true },
          { code: 'M', name: 'Medium', available: true },
          { code: 'L', name: 'Large', available: true },
          { code: 'XL', name: 'Extra Large', available: true }
        ],
        customWriting: {
          enabled: true,
          maxLength: 50,
          fee: 5
        }
      };

      res.status(200).json({
        success: true,
        data: customizationOptions
      });

    } catch (error) {
      console.error('Get customization options error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Validation helper
  private static validateCustomization(customization: any): string | null {
    if (!customization.style) {
      return 'Style is required';
    }

    if (!customization.color) {
      return 'Color is required';
    }

    if (!customization.size) {
      return 'Size is required';
    }

    if (customization.customWriting?.text) {
      if (customization.customWriting.text.length > 50) {
        return 'Custom text cannot exceed 50 characters';
      }
      
      if (!['left', 'center', 'right'].includes(customization.customWriting.alignment)) {
        return 'Invalid text alignment';
      }
    }

    return null;
  }
}
