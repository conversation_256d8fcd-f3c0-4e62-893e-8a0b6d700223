import express from 'express';
import { getSavedAdd<PERSON>, addAddress, updateAddress, 
    deleteAddress, setDefaultAddress, getAddressById } from '../controllers/index';
import { verifyTokenForUser } from '../middlewares/auth.middlewares';

const router = express.Router();

// All routes require user authentication
router.use(verifyTokenForUser);

// Get all user's saved addresses
router.get('/', getSavedAddresses);

// Get single address by ID
router.get('/:addressId', getAddressById);

// Add new address
router.post('/', addAddress);

// Update existing address
router.put('/:addressId', updateAddress);

// Delete address
router.delete('/:addressId', deleteAddress);

// Set default address
router.patch('/:addressId/default', setDefaultAddress);

export default router;
