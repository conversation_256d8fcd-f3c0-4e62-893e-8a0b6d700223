// import * as emailjs from 'emailjs';


// // Type definitions for the template parameters
// interface TemplateParams {
//   to_email: string;
//   passcode: string;
//   time: string;
// }

// const EMAILJS_USER_ID = '<EMAIL>';  
// const EMAILJS_SERVICE_ID = 'service_ol21xvq';    
// const EMAILJS_TEMPLATE_ID = 'template_0em1tt3'; 

// // Function to send an OTP email
// const sendOtpEmail = (toEmail: string, passcode: string, expirationTime: string): void => {
//   // Prepare the dynamic template parameters
//   const templateParams: TemplateParams = {
//     to_email: toEmail,          // Recipient email
//     passcode: passcode,         // OTP passcode
//     time: expirationTime,       // OTP expiration time (e.g., "5:30 PM")
//   };

//   // Create the EmailJS server instance
//   const server = emailjs.server.connect({
//     user: EMAILJS_USER_ID,
//     password: 'your_emailjs_password', // Your EmailJS password (or use an API key)
//     host: 'smtp.emailjs.com',  // EmailJS SMTP server address
//     ssl: true
//   });

//   // Send the email using the server instance
//   server.send(
//     {
//       text: `Your OTP is ${passcode}. It expires at ${expirationTime}.`,
//       from: '<EMAIL>',
//       to: toEmail,
//       subject: 'Your OTP Code',
//       attachment: [
//         {
//           data: `
//             To authenticate, please use the following One Time Password (OTP):

//             ${passcode}

//             This OTP will be valid for 2 minutes till ${expirationTime}.

//             Do not share this OTP with anyone. If you didn't make this request, you can safely ignore this email.
//             OYRQ will never contact you about this email or ask for any login codes or links. Beware of phishing scams.

//             Thanks for visiting OYRQ!
//           `,
//           alternative: true
//         }
//       ]
//     },
//     (err, message) => {
//       if (err) {
//         console.error('Error sending email:', err);
//       } else {
//         console.log('Email sent successfully:', message);
//       }
//     }
//   );
// };

// // Example usage: Send OTP email with passcode and expiration time
// const passcode: string = Math.floor(100000 + Math.random() * 900000).toString(); // Generate a random 6-digit OTP
// const expirationTime: string = new Date(new Date().getTime() + 2 * 60000).toLocaleTimeString(); // OTP expires in 2 minutes

// // Call the function to send the email
// sendOtpEmail('<EMAIL>', passcode, expirationTime);
