import nodemailer from 'nodemailer';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Get Gmail credentials from environment variables
const { EMAIL_HOST, EMAIL_PORT, EMAIL_USER, EMAIL_PASS } = process.env;

// Create a reusable transporter object using the default SMTP transport
const transporter = nodemailer.createTransport({
  host: EMAIL_HOST,
  port: Number(EMAIL_PORT),
  secure: false, // true for 465, false for other ports
  auth: {
    user: EMAIL_USER,
    pass: EMAIL_PASS,
  },
});

// Function to send a welcome email
const sendWelcomeEmail = (toEmail: string) => {
  // Set up email data
  const mailOptions = {
    from: EMAIL_USER, // sender address
    to: toEmail, // list of receivers
    subject: 'Welcome to Our Service', // subject line
    text: 'Hello, welcome to our service! We are glad to have you onboard.', // plain text body
    html: '<b>Hello, welcome to our service! We are glad to have you onboard.</b>', // HTML body
  };

  // Send email
  transporter.sendMail(mailOptions, (error, info) => {
    if (error) {
      return console.log('Error sending email: ', error);
    }
    console.log('Email sent: ' + info.response);
  });
};

// Export the function for use in other files
export { sendWelcomeEmail };






















// // ../utils/testsmtp.js

// import net from 'net';
// import {Request, Response} from 'express';

// /**
//  * Test connection to the SMTP server.
//  * @param {string} smtpHost - SMTP server address (e.g., 'smtp.gmail.com').
//  * @param {number} smtpPort - SMTP port (587 for TLS, 465 for SSL).
//  * @returns {Promise<string>} - Returns a promise that resolves to the result of the connection test.
//  */
// export const testsmtp = (req: Request , res:Response) => {
//   const smtpHost = 'smtp.gmail.com';  // SMTP server address
//   const smtpPort = 587;  // SMTP port (587 for TLS)

//   const client = new net.Socket();

//   client.setTimeout(10000); // Set a timeout (e.g., 10 seconds)

//   client.connect(smtpPort, smtpHost, () => {
//     console.log('Connection successful');
//     client.end(); // Close the connection once done
//     res.status(200).json({ message: 'Connection successful' });  // Return success response
//   });

//   client.on('timeout', () => {
//     console.log('Connection timed out');
//     client.destroy();
//     res.status(500).json({ error: 'Connection timed out' });  // Return timeout response
//   });

//   client.on('error', (err) => {
//     console.log('Error:', err.message);
//     client.destroy();
//     res.status(500).json({ error: `Error: ${err.message}` });  // Return error response
//   });

//   client.on('close', () => {
//     console.log('Connection closed');
//   });
// };



