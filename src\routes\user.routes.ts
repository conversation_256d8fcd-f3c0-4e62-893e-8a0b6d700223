import { Router } from 'express';
import {
  updateUserInfo,
  deleteUserProfile,
  getUserInfo,
  updatePassword
} from '../controllers/user.controller';
import { verifyTokenForUser } from '../middlewares/auth.middlewares';
import { validateMedia } from '../utils/validateMedia';

const router = Router();

// All user routes require authentication
router.use(verifyTokenForUser);

// Update user info (fullname, phoneNumber, profilePicture)
router.put('/update-info', validateMedia, updateUserInfo);

// Delete user profile
router.delete('/delete-profile', deleteUserProfile);

// Get user info
router.get('/info', getUserInfo);

// Update password
router.put('/update-password', updatePassword);

export default router;
