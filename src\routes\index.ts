export { default as authRoutes } from './auth.routes';
export { default as shopRoutes } from './shop.routes';
export { default as categoryRoutes } from './category.routes';
export { default as subcategoryRoutes } from './subcategory.routes';
export { default as productRoutes } from './product.routes';
export { default as adminRoutes } from './admin.routes';
export { default as wishlistRoutes } from './wishlist.routes';
export { default as userRoutes } from './user.routes';
export { default as brandOwnerRoutes } from './brandOwner.routes';
export { default as customProductRoutes } from './customProduct.routes';
export { default as cartRoutes } from './cart.routes';
export { default as orderRoutes } from './order.routes';
export { default as businessOrderRoutes } from './businessOrder.routes';
export { default as checkoutRoutes } from './checkout.routes';
export { default as addressRoutes } from './address.routes';
export { default as notificationRoutes } from './notification.routes';
//export { default as smtpRoutes } from './smtp';
