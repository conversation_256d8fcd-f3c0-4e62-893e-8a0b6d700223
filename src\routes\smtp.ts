// // ../router/testsmtp.js

// import express from 'express';
// import { Router } from 'express';
// import { sendWelcomeEmail } from '../utils/testsmtp';  // Import the testsmtp function from utils

// const router = Router();

// // Route that calls the testsmtp function
// router.post('/send-welcome-email', (req, res) => {
//   // Extract email address from the request body
//   const { email } = req.body;

//   if (!email) {
//     return res.status(400).json({ message: 'Email is required' });
//   }

//   // Send the welcome email
//   sendWelcomeEmail(email);

//   // Respond to the client
//   return res.status(200).json({ message: 'Welcome email sent!' });
// });

// export default router;
