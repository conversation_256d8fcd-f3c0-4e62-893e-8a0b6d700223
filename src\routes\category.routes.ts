import { Router } from 'express';
import {
  createCategory,
  getAllCategories,
  getCategoryDetails,
  updateCategory,
  deleteCategory,
  getAllCategoriesByShop
} from '../controllers/category.controller';
import { verifyTokenForAdmin } from '../middlewares/auth.middlewares';
import { validateMedia } from '../utils/validateMedia';

const router = Router();

// Public routes - get categories (no authentication required)
router.get('/', getAllCategories);
router.get('/shop/:shopId', getAllCategoriesByShop);
router.get('/:categoryId', getCategoryDetails);

// Protected routes - require admin authentication
router.post('/', verifyTokenForAdmin, validateMedia, createCategory);
router.put('/:categoryId', verifyTokenForAdmin, validateMedia, updateCategory);
router.delete('/:categoryId', verifyTokenForAdmin, deleteCategory);

export default router;
