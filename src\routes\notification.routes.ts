import { Router } from 'express';
import {
  getAllNotifications,
  readNotification,
  readAllNotifications,
  deleteNotification,
  clearAllNotifications
} from '../controllers/notification.controller';
import { verifyToken } from '../middlewares/auth.middlewares';

const router = Router();

// Apply authentication to all notification routes
router.use(verifyToken);

// Get all notifications for the authenticated user
// Query params: page, limit, isRead, notificationType
router.get('/', getAllNotifications);

// Mark a specific notification as read
router.patch('/:notificationId/read', readNotification);

// Mark all notifications as read for the authenticated user
router.patch('/read-all', readAllNotifications);

// Delete a specific notification
router.delete('/:notificationId', deleteNotification);

// Clear all notifications for the authenticated user
router.delete('/', clearAllNotifications);

export default router;
