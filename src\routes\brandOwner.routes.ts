import { Router } from 'express';
import {
  signUpBrandOwner,
  updateBrandOwnerInfo,
  updateBrandOwnerPassword,
  deleteBrand,
  getBrandInfo
} from '../controllers/index';
import { validateSignUpBrandOwner, validateUpdateBrandOwnerInfo, validateUpdateBrandOwnerPassword, 
  validateDeleteBrand, validateGetBrandInfo } from '../validators/brandOwner.validator';
import { verifyTokenForbrandOwner } from '../middlewares/auth.middlewares';
import { validateMedia } from '../utils/validateMedia';

const router = Router();


router.post('/signup', validateMedia, signUpBrandOwner);


router.use(verifyTokenForbrandOwner);

router.put('/update-info', validateMedia, updateBrandOwnerInfo);
router.put('/update-password', updateBrandOwnerPassword);
router.delete('/delete-brand', deleteBrand);
router.get('/info',  getBrandInfo);

export default router;
