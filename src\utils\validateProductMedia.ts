import { Request, Response, NextFunction } from "express";
import multer, { MulterError } from "multer";

// Configure multer for multiple file uploads with different fields
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50 MB per file
    files: 11 // Maximum 11 files per upload (10 product media + 1 size chart)
  },
  fileFilter: (req, file, cb) => {
    // Accept images only
    if (file.mimetype.startsWith("image/")) {
      cb(null, true);
    } else {
      cb(new Error("Only image files are allowed (jpg, png, gif, etc.)"));
    }
  }
}).fields([
  { name: 'productMedia', maxCount: 10 }, // Up to 10 product media files
  { name: 'sizeChartImage', maxCount: 1 } // Single size chart image
]);

export const validateProductMedia = (req: Request, res: Response, next: NextFunction) => {
  upload(req, res, (error: any) => {
    if (error) {
      console.error("Multer Error:", error);
      if (error instanceof MulterError) {
        if (error.code === "LIMIT_FILE_SIZE") {
          res.status(400).json({ 
            success: false,
            message: "File size too large. Maximum 50MB per file." 
          });
        } else if (error.code === "LIMIT_FILE_COUNT") {
          res.status(400).json({ 
            success: false,
            message: "Too many files. Maximum 10 product media files and 1 size chart image allowed." 
          });
        } else if (error.code === "LIMIT_UNEXPECTED_FILE") {
          res.status(400).json({ 
            success: false,
            message: "Unexpected file field. Only 'productMedia' and 'sizeChartImage' fields are allowed." 
          });
        } else {
          res.status(400).json({ 
            success: false,
            message: `Upload Error: ${error.message}` 
          });
        }
      } else {
        res.status(400).json({ 
          success: false,
          message: `Upload Error: ${error.message}` 
        });
      }
      req.destroy();
      return;
    }
    next();
  });
};