import mongoose, { Schema } from 'mongoose';
import { ICart, ICartItem } from '../interfaces/cart.interfaces';

const cartItemSchema = new Schema<ICartItem>({
  product: {
    type: Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: 1,
    default: 1
  },
  size: {
    type: String,
    enum: ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'],
    required: true
  },
  color: {
    type: String,
    required: true
  },
  priceAtTime: {
    type: Number,
    required: true,
    min: 0
  }
});

const cartSchema = new Schema<ICart>({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  items: [cartItemSchema],
  totalAmount: {
    type: Number,
    required: true,
    default: 0,
    min: 0
  },
  appliedPromoCode: {
    type: String,
    default: null
  },
  discountAmount: {
    type: Number,
    default: 0,
    min: 0
  }
}, {
  timestamps: true
});

// Calculate total amount before saving
cartSchema.pre('save', function(next) {
  this.totalAmount = this.items.reduce((total, item) => {
    return total + (item.priceAtTime * item.quantity);
  }, 0);
  next();
});

// Index for faster queries
cartSchema.index({ user: 1 });

export default mongoose.model<ICart>('Cart', cartSchema);
