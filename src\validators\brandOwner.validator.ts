// expressValidation.ts
import { body, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';

// Validate sign-up fields
export const validateSignUpBrandOwner = [
  body('role').isIn(['brandOwner']).withMessage('Invalid role for brand owner signup'),
  body('brandName').trim().isLength({ min: 1, max: 100 }).withMessage('Brand name must be between 1 and 100 characters'),
  body('email').isEmail().withMessage('Please enter a valid email address'),
  body('phone').matches(/^\+?[\d\s-()]+$/).withMessage('Please enter a valid phone number'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters long'),
  body('confirmPassword').custom((value, { req }) => {
    if (value !== req.body.password) {
      throw new Error('Passwords do not match');
    }
    return true;
  }),
  body('websitelink').optional().matches(/^https?:\/\/.+/).withMessage('Please enter a valid website URL'),
  
  // Custom validation to check for validation errors
  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(StatusCodes.BAD_REQUEST).json({ success: false, errors: errors.array() });
    }
    next();
  }
];

// Validate update brand owner info fields
export const validateUpdateBrandOwnerInfo = [
  body('brandName').optional().trim().isLength({ min: 1, max: 100 }).withMessage('Brand name must be between 1 and 100 characters'),
  body('address').optional().isLength({ max: 500 }).withMessage('Address cannot exceed 500 characters'),
  body('websitelink').optional().matches(/^https?:\/\/.+/).withMessage('Please enter a valid website URL'),
  body('bio').optional().isLength({ max: 1000 }).withMessage('Bio cannot exceed 1000 characters'),

  // Custom validation to check for validation errors
  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(StatusCodes.BAD_REQUEST).json({ success: false, errors: errors.array() });
    }
    next();
  }
];

// Validate update brand owner password fields
export const validateUpdateBrandOwnerPassword = [
  body('oldPassword').notEmpty().withMessage('Old password is required'),
  body('newPassword').isLength({ min: 6 }).withMessage('New password must be at least 6 characters long'),
  body('confirmPassword').custom((value, { req }) => {
    if (value !== req.body.newPassword) {
      throw new Error('New password and confirm password do not match');
    }
    return true;
  }),

  // Custom validation to check for validation errors
  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(StatusCodes.BAD_REQUEST).json({ success: false, errors: errors.array() });
    }
    next();
  }
];

// Validate delete brand request
export const validateDeleteBrand = [
  // Check for user authentication
  (req: Request, res: Response, next: NextFunction) => {
    if (!req.user?._id) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ success: false, message: 'User not authenticated' });
    }
    next();
  }
];

// Validate get brand info request
export const validateGetBrandInfo = [
  // Check for user authentication
  (req: Request, res: Response, next: NextFunction) => {
    if (!req.user?._id) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ success: false, message: 'User not authenticated' });
    }
    next();
  }
];
