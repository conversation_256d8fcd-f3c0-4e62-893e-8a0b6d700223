import { Request, Response } from 'express';
import Order from '../models/Order';
import Product from '../models/Product';
import Shop from '../models/Shop';
import { OrderStatus } from '../interfaces/order.interfaces';
import { IBusinessOrderFilters, IUpdateBusinessOrderRequest } from '../interfaces/businessOrder.interfaces';
import { notifyOrderStatusChanged, getBrandOwnerIdsFromOrder } from '../services/notification.service';


export const getShopOrders = async (req: Request, res: Response) => {
  try {
      const userId = req.user!._id;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      
      // Find all shops owned by this business owner
      const shops = await Shop.find({ userId: userId });
      if (shops.length === 0) {
        return res.status(200).json({
          success: true,
          data: {
            orders: [],
            pagination: {
              currentPage: page,
              totalPages: 0,
              totalOrders: 0,
              hasNextPage: false,
              hasPrevPage: false
            }
          }
        });
      }

      const shopIds = shops.map(shop => shop._id);
      const skip = (page - 1) * limit;

      // Get all products from user's shops
      const products = await Product.find({ shop: { $in: shopIds } }).select('_id');
      const productIds = products.map(p => p._id);
      
      if (productIds.length === 0) {
        return res.status(200).json({
          success: true,
          data: {
            orders: [],
            pagination: {
              currentPage: page,
              totalPages: 0,
              totalOrders: 0,
              hasNextPage: false,
              hasPrevPage: false
            }
          }
        });
      }

      // Build base filters for orders that contain products from user's shops
      const baseFilters: any = {
        'items.product': { $in: productIds }
      };
      
      // Add additional filters
      if (req.query.status) {
        baseFilters.orderStatus = req.query.status;
      }
      
      if (req.query.dateFrom || req.query.dateTo) {
        baseFilters.createdAt = {};
        if (req.query.dateFrom) {
          baseFilters.createdAt.$gte = new Date(req.query.dateFrom as string);
        }
        if (req.query.dateTo) {
          baseFilters.createdAt.$lte = new Date(req.query.dateTo as string);
        }
      }

      if (req.query.minAmount || req.query.maxAmount) {
        baseFilters.totalAmount = {};
        if (req.query.minAmount) {
          baseFilters.totalAmount.$gte = parseFloat(req.query.minAmount as string);
        }
        if (req.query.maxAmount) {
          baseFilters.totalAmount.$lte = parseFloat(req.query.maxAmount as string);
        }
      }

      // Get orders using aggregation for better filtering and counting
      const aggregationPipeline: any[] = [
        { $match: baseFilters },
        {
          $lookup: {
            from: 'users',
            localField: 'user',
            foreignField: '_id',
            as: 'user'
          }
        },
        {
          $lookup: {
            from: 'deliveryaddresses',
            localField: 'deliveryAddress',
            foreignField: '_id',
            as: 'deliveryAddress'
          }
        },
        {
          $lookup: {
            from: 'products',
            localField: 'items.product',
            foreignField: '_id',
            as: 'productDetails'
          }
        },
        {
          $addFields: {
            relevantItems: {
              $filter: {
                input: '$items',
                as: 'item',
                cond: { $in: ['$$item.product', productIds] }
              }
            }
          }
        },
        {
          $match: {
            'relevantItems.0': { $exists: true } // Only include orders with relevant items
          }
        },
        { $sort: { createdAt: -1 as -1 } },
        {
          $facet: {
            data: [
              { $skip: skip },
              { $limit: limit }
            ],
            totalCount: [
              { $count: 'count' }
            ]
          }
        }
      ];

      const result = await Order.aggregate(aggregationPipeline);
      const orders = result[0].data;
      const totalOrders = result[0].totalCount[0]?.count || 0;

      // Format the response
      const formattedOrders = orders.map((order: any) => {
        const user = order.user[0];
        const relevantTotal = order.relevantItems.reduce((sum: number, item: any) => sum + item.totalPrice, 0);
        
        return {
          id: order._id,
          orderNumber: `#ORD${order.orderNumber}`,
          customerName: user?.fullname || 'Unknown Customer',
          date: order.createdAt,
          total: order.totalAmount,
          status: order.orderStatus,
          relevantTotal: relevantTotal,
          relevantItems: order.relevantItems.length
        };
      });

      res.status(200).json({
        success: true,
        data: {
          orders: formattedOrders,
          pagination: {
            currentPage: page,
            totalPages: Math.ceil(totalOrders / limit),
            totalOrders,
            hasNextPage: page < Math.ceil(totalOrders / limit),
            hasPrevPage: page > 1
          }
        }
      });

    } catch (error: any) {
      console.error('Get shop orders error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
};

export const getOrderById = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;
      const { orderId } = req.params;

      // Validate orderId is a valid ObjectId
      if (!orderId || !orderId.match(/^[0-9a-fA-F]{24}$/)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid order ID format'
        });
      }

      // Find all shops owned by this business owner
      const shops = await Shop.find({ userId: userId });
      if (shops.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'No shops found for this business owner'
        });
      }

      const shopIds = shops.map(shop => shop._id);

      const order = await Order.findById(orderId)
        .populate('user', 'fullname email phoneNumber')
        .populate({
          path: 'deliveryAddress',
          populate: {
            path: 'user',
            select: 'fullname phoneNumber'
          }
        })
        .populate({
          path: 'items.product',
          select: 'productName productMedia price shop',
          populate: {
            path: 'shop',
            select: 'shopName userId'
          }
        });

      if (!order) {
        return res.status(404).json({
          success: false,
          message: 'Order not found'
        });
      }

      // Filter items to only include products from business owner's shops
      const relevantItems = order.items.filter(item => {
        const product = item.product as any;
        if (!product) return false;
        
        // Handle both cases: shop as ObjectId or populated shop object
        const productShopId = product.shop?._id 
          ? product.shop._id.toString() 
          : product.shop?.toString();
          
        return productShopId && shopIds.some(shopId => shopId.toString() === productShopId);
      });

      if (relevantItems.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'No items in this order belong to your shops'
        });
      }

      // Format response according to requirements
      const user = order.user as any;
      const deliveryAddress = order.deliveryAddress as any;
      const orderSubtotal = relevantItems.reduce((sum, item) => sum + item.totalPrice, 0);
      
      const filteredOrder = {
        orderStatus: order.orderStatus,
        customerDetails: {
          orderDate: order.createdAt,
          customerName: user?.fullname || deliveryAddress?.user?.fullname || 'Unknown Customer',
          phone: user?.phoneNumber || deliveryAddress?.user?.phoneNumber || 'N/A',
          email: user?.email || 'N/A',
          shippingAddress: {
            fullName: deliveryAddress?.user?.fullname || user?.fullname || 'N/A',
            streetAddress: deliveryAddress?.streetAddress || 'N/A',
            apartment: deliveryAddress?.apartment || '',
            state: deliveryAddress?.state || 'N/A',
            city: deliveryAddress?.city || 'N/A',
            postalCode: deliveryAddress?.postalCode || 'N/A',
            phoneNumber: deliveryAddress?.user?.phoneNumber || user?.phoneNumber || 'N/A'
          }
        },
        orderDetails: {
          orderNumber: `#ORD${order.orderNumber}`,
          items: relevantItems.map(item => {
            const product = item.product as any;
            return {
              productName: item.productName,
              productMedia: product?.productMedia || [],
              color: item.color,
              size: item.size,
              quantity: item.quantity,
              priceAtTime: item.priceAtTime,
              total: item.totalPrice
            };
          }),
          subtotal: orderSubtotal,
          shippingCost: order.shippingCost || 0,
          tax: order.tax || 0,
          discountAmount: order.discountAmount || 0,
          totalAmount: order.totalAmount,
          relevantTotal: orderSubtotal
        }
      };

      res.status(200).json({
        success: true,
        data: filteredOrder
      });

    } catch (error: any) {
      console.error('Get order by ID error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
    });
  }
};

export const updateOrderStatus = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;
      const { orderId } = req.params;
      const { orderStatus, notes }: IUpdateBusinessOrderRequest = req.body;

      // Validate orderId is a valid ObjectId
      if (!orderId || !orderId.match(/^[0-9a-fA-F]{24}$/)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid order ID format'
        });
      }

      // Find all shops owned by this business owner
      const shops = await Shop.find({ userId: userId });
      if (shops.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'No shops found for this business owner'
        });
      }

      const shopIds = shops.map(shop => shop._id);

      const order = await Order.findById(orderId).populate('items.product');

      if (!order) {
        return res.status(404).json({
          success: false,
          message: 'Order not found'
        });
      }

      // Check if business owner has products in this order
      const hasRelevantItems = order.items.some(item => {
        const product = item.product as any;
        return product && shopIds.some(shopId => shopId.toString() === product.shop.toString());
      });

      if (!hasRelevantItems) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to update this order'
        });
      }

      // Business owners can update to any valid order status
      const allowedStatuses = Object.values(OrderStatus);
      
      if (orderStatus && !allowedStatuses.includes(orderStatus as OrderStatus)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid order status provided'
        });
      }

      // Update order
      if (orderStatus) {
        order.orderStatus = orderStatus as OrderStatus;
      }

      if (notes !== undefined) {
        order.notes = notes;
      }

      await order.save();

      // Send notifications about order status change
      if (orderStatus) {
        try {
          const brandOwnerIds = await getBrandOwnerIdsFromOrder(order._id);
          await notifyOrderStatusChanged(
            order._id,
            order.orderNumber!,
            orderStatus,
            order.user,
            brandOwnerIds
          );
        } catch (notificationError) {
          console.error('Error sending order status change notifications:', notificationError);
          // Don't fail the order update if notification fails
        }
      }

      res.status(200).json({
        success: true,
        message: 'Order updated successfully',
        data: order
      });

    } catch (error: any) {
      console.error('Update order error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
};

export const getOrderStats = async (req: Request, res: Response) => {
  try {
    const userId = req.user!._id;
    const timePeriod = req.query.timePeriod as string; // 'today', 'thisMonth', 'thisYear'

    // Find all shops owned by this business owner
    const shops = await Shop.find({ userId: userId });
    if (shops.length === 0) {
      return res.status(200).json({
        success: true,
        data: {
          totalOrdersCount: 0,
          totalRevenue: 0,
          pendingOrders: 0,
          processingOrders: 0,
          shippedOrders: 0,
          deliveredOrders: 0,
          cancelledOrders: 0,
          refundedOrders: 0,
          averageOrderValue: 0,
          lowStockProducts: 0,
          totalProductsSold: 0,
          timePeriod: timePeriod || 'allTime',
          ordersComparison: {
            isIncrease: false,
            percentage: 0
          },
          revenueComparison: {
            isIncrease: false,
            percentage: 0
          },
          productsSoldComparison: {
            isIncrease: false,
            percentage: 0
          }
        }
      });
    }

    const shopIds = shops.map(shop => shop._id);

    // Get all products from user's shops
    const products = await Product.find({ shop: { $in: shopIds } }).select('_id');
    const productIds = products.map(p => p._id);

    // Build date filters for current and previous periods
    const now = new Date();
    let currentPeriodFilter = {};
    let previousPeriodFilter = {};

    if (timePeriod === 'today') {
      // Current day
      const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
      currentPeriodFilter = {
        createdAt: {
          $gte: startOfDay,
          $lt: endOfDay
        }
      };

      // Previous day
      const startOfPreviousDay = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
      const endOfPreviousDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      previousPeriodFilter = {
        createdAt: {
          $gte: startOfPreviousDay,
          $lt: endOfPreviousDay
        }
      };
    } else if (timePeriod === 'thisMonth') {
      // Current month
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
      currentPeriodFilter = {
        createdAt: {
          $gte: startOfMonth,
          $lt: endOfMonth
        }
      };

      // Previous month
      const startOfPreviousMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const endOfPreviousMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      previousPeriodFilter = {
        createdAt: {
          $gte: startOfPreviousMonth,
          $lt: endOfPreviousMonth
        }
      };
    } else if (timePeriod === 'thisYear') {
      // Current year
      const startOfYear = new Date(now.getFullYear(), 0, 1);
      const endOfYear = new Date(now.getFullYear() + 1, 0, 1);
      currentPeriodFilter = {
        createdAt: {
          $gte: startOfYear,
          $lt: endOfYear
        }
      };

      // Previous year
      const startOfPreviousYear = new Date(now.getFullYear() - 1, 0, 1);
      const endOfPreviousYear = new Date(now.getFullYear(), 0, 1);
      previousPeriodFilter = {
        createdAt: {
          $gte: startOfPreviousYear,
          $lt: endOfPreviousYear
        }
      };
    }

    // Helper function to get stats for a period
    const getStatsForPeriod = async (dateFilter: any) => {
      const stats = await Order.aggregate([
        {
          $match: {
            'items.product': { $in: productIds },
            ...dateFilter
          }
        },
        {
          $addFields: {
            relevantItems: {
              $filter: {
                input: '$items',
                as: 'item',
                cond: { $in: ['$$item.product', productIds] }
              }
            }
          }
        },
        {
          $match: {
            'relevantItems.0': { $exists: true } // Only include orders with relevant items
          }
        },
        {
          $addFields: {
            relevantRevenue: {
              $sum: '$relevantItems.totalPrice'
            }
          }
        },
        {
          $group: {
            _id: null,
            totalOrdersCount: { $sum: 1 },
            totalRevenue: { $sum: '$relevantRevenue' },
            pendingOrders: {
              $sum: { $cond: [{ $eq: ['$orderStatus', OrderStatus.PENDING] }, 1, 0] }
            },
            processingOrders: {
              $sum: { $cond: [{ $eq: ['$orderStatus', OrderStatus.PROCESSING] }, 1, 0] }
            },
            shippedOrders: {
              $sum: { $cond: [{ $eq: ['$orderStatus', OrderStatus.SHIPPED] }, 1, 0] }
            },
            deliveredOrders: {
              $sum: { $cond: [{ $eq: ['$orderStatus', OrderStatus.DELIVERED] }, 1, 0] }
            },
            cancelledOrders: {
              $sum: { $cond: [{ $eq: ['$orderStatus', OrderStatus.CANCELLED] }, 1, 0] }
            },
            refundedOrders: {
              $sum: { $cond: [{ $eq: ['$orderStatus', OrderStatus.REFUNDED] }, 1, 0] }
            }
          }
        },
        {
          $addFields: {
            averageOrderValue: {
              $cond: [
                { $gt: ['$totalOrdersCount', 0] },
                { $divide: ['$totalRevenue', '$totalOrdersCount'] },
                0
              ]
            }
          }
        }
      ]);

      return stats[0] || {
        totalOrdersCount: 0,
        totalRevenue: 0,
        pendingOrders: 0,
        processingOrders: 0,
        shippedOrders: 0,
        deliveredOrders: 0,
        cancelledOrders: 0,
        refundedOrders: 0,
        averageOrderValue: 0
      };
    };

    // Enhanced helper function to get total products sold for a period
    const getProductsSoldForPeriod = async (dateFilter: any) => {
      try {
        if (Object.keys(dateFilter).length === 0) {
          // For all-time, use the existing stockSold field
          const result = await Product.aggregate([
            {
              $match: {
                shop: { $in: shopIds },
                isActive: true
              }
            },
            {
              $group: {
                _id: null,
                totalSold: { $sum: '$stockSold' }
              }
            }
          ]);
          return result[0]?.totalSold || 0;
        }

        // For specific time periods, calculate from orders
        const result = await Order.aggregate([
          {
            $match: {
              ...dateFilter,
              paymentStatus: { $ne: 'PENDING' }, // Only count paid orders
              orderStatus: { $nin: [OrderStatus.CANCELLED, OrderStatus.REFUNDED] } // Exclude cancelled/refunded
            }
          },
          {
            $unwind: '$items'
          },
          {
            $match: {
              'items.product': { $in: productIds }
            }
          },
          {
            $group: {
              _id: null,
              totalQuantitySold: { $sum: '$items.quantity' }
            }
          }
        ]);

        const totalSold = result[0]?.totalQuantitySold || 0;
        console.log(`Products sold for period:`, { dateFilter, totalSold });
        return totalSold;
      } catch (error) {
        console.error('Error in getProductsSoldForPeriod:', error);
        return 0;
      }
    };

    const calculatePercentageChange = (current: number, previous: number) => {
      console.log(`Calculating percentage change: current=${current}, previous=${previous}`);
      
      if (previous === 0) {
        if (current === 0) {
          return {
            isIncrease: false,
            percentage: 0
          };
        }
        // When previous is 0 and current > 0, show it as the actual increase
        // For example: 0 to 54 products = 5400% increase (54 * 100)
        const result = {
          isIncrease: true,
          percentage: current * 100 // This represents the actual growth from 0
        };
        console.log(`Previous was 0, current is ${current}, showing as:`, result);
        return result;
      }
      
      // Calculate the actual percentage change
      const percentageChange = ((current - previous) / previous) * 100;
      console.log(`Raw percentage change: ${percentageChange}`);
      
      // Handle equal values (no change)
      if (current === previous) {
        return {
          isIncrease: false,
          percentage: 0
        };
      }
      
      // Return the actual percentage (positive or negative)
      const result = {
        isIncrease: percentageChange > 0,
        percentage: Math.round(percentageChange * 100) / 100
      };
      
      console.log(`Final result:`, result);
      return result;
    };

    // Get current period stats
    let currentStats;
    let previousStats = null;
    let currentProductsSold = 0;
    let previousProductsSold = 0;
    let ordersComparison = { isIncrease: false, percentage: 0 };
    let revenueComparison = { isIncrease: false, percentage: 0 };
    let productsSoldComparison = { isIncrease: false, percentage: 0 };

    if (timePeriod && ['today', 'thisMonth', 'thisYear'].includes(timePeriod)) {
      // Get stats for both current and previous periods
      console.log('Getting current period stats...');
      currentStats = await getStatsForPeriod(currentPeriodFilter);
      
      console.log('Getting previous period stats...');
      previousStats = await getStatsForPeriod(previousPeriodFilter);
      
      // Get products sold for both periods
      console.log('Getting current period products sold...');
      currentProductsSold = await getProductsSoldForPeriod(currentPeriodFilter);
      
      console.log('Getting previous period products sold...');
      previousProductsSold = await getProductsSoldForPeriod(previousPeriodFilter);

      console.log('Products sold comparison data:', {
        current: currentProductsSold,
        previous: previousProductsSold,
        timePeriod
      });

      // Calculate comparisons
      ordersComparison = calculatePercentageChange(
        currentStats.totalOrdersCount, 
        previousStats.totalOrdersCount
      );
      revenueComparison = calculatePercentageChange(
        currentStats.totalRevenue, 
        previousStats.totalRevenue
      );
      productsSoldComparison = calculatePercentageChange(
        currentProductsSold,
        previousProductsSold
      );

      console.log('Final comparisons:', {
        ordersComparison,
        revenueComparison,
        productsSoldComparison
      });
    } else {
      // For 'allTime' or invalid timePeriod, get all-time stats
      currentStats = await getStatsForPeriod({});
      currentProductsSold = await getProductsSoldForPeriod({});
    }

    // Get low stock products count
    const lowStockProducts = await Product.countDocuments({
      shop: { $in: shopIds },
      isLowStock: true,
      isActive: true
    });

    // Build final result
    const result = {
      ...currentStats,
      lowStockProducts,
      totalProductsSold: currentProductsSold,
      timePeriod: timePeriod || 'allTime',
      ordersComparison,
      revenueComparison,
      productsSoldComparison
    };

    console.log('Final result:', JSON.stringify(result, null, 2));

    res.status(200).json({
      success: true,
      data: result
    });

  } catch (error: any) {
    console.error('Get order stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

export const getTopSellingProducts = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;
      const timePeriod = req.query.timePeriod as string; // 'today', 'thisMonth', 'thisYear'
      const limit = parseInt(req.query.limit as string) || 10; // Default to top 10

      // Find all shops owned by this business owner
      const shops = await Shop.find({ userId: userId });
      if (shops.length === 0) {
        return res.status(200).json({
          success: true,
          data: {
            topSellingProducts: [],
            timePeriod: timePeriod || 'allTime',
            totalProducts: 0
          }
        });
      }

      const shopIds = shops.map(shop => shop._id);

      // Get all products from user's shops
      const products = await Product.find({ shop: { $in: shopIds } }).select('_id');
      const productIds = products.map(p => p._id);

      // Build date filter based on time period
      let dateFilter = {};
      const now = new Date();

      if (timePeriod === 'today') {
        const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
        dateFilter = {
          createdAt: {
            $gte: startOfDay,
            $lt: endOfDay
          }
        };
      } else if (timePeriod === 'thisMonth') {
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
        dateFilter = {
          createdAt: {
            $gte: startOfMonth,
            $lt: endOfMonth
          }
        };
      } else if (timePeriod === 'thisYear') {
        const startOfYear = new Date(now.getFullYear(), 0, 1);
        const endOfYear = new Date(now.getFullYear() + 1, 0, 1);
        dateFilter = {
          createdAt: {
            $gte: startOfYear,
            $lt: endOfYear
          }
        };
      }
      // If no timePeriod or invalid, use all time (no date filter)

      // Aggregate to get top selling products
      const topSellingProducts = await Order.aggregate([
        {
          $match: {
            'items.product': { $in: productIds },
            ...dateFilter
          }
        },
        {
          $unwind: '$items'
        },
        {
          $match: {
            'items.product': { $in: productIds }
          }
        },
        {
          $group: {
            _id: '$items.product',
            totalSold: { $sum: '$items.quantity' },
            totalRevenue: { $sum: '$items.totalPrice' }
          }
        },
        {
          $sort: { totalSold: -1 }
        },
        {
          $limit: limit
        },
        {
          $lookup: {
            from: 'products',
            localField: '_id',
            foreignField: '_id',
            as: 'product'
          }
        },
        {
          $unwind: '$product'
        },
        {
          $project: {
            _id: 0,
            productId: '$_id',
            productName: '$product.productName',
            productImage: { $arrayElemAt: ['$product.productMedia', 0] },
            timesSold: '$totalSold',
            totalRevenue: '$totalRevenue',
            price: '$product.price'
          }
        }
      ]);

      res.status(200).json({
        success: true,
        data: {
          topSellingProducts,
          timePeriod: timePeriod || 'allTime',
          totalProducts: topSellingProducts.length
        }
      });

    } catch (error: any) {
      console.error('Get top selling products error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
};

export const getOrdersPerMonth = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;
      const year = parseInt(req.query.year as string) || new Date().getFullYear();

      // Find all shops owned by this business owner
      const shops = await Shop.find({ userId: userId });
      if (shops.length === 0) {
        return res.status(200).json({
          success: true,
          message: `Orders per month for ${year}`,
          data: {
            year,
            monthlyData: Array.from({ length: 12 }, (_, index) => ({
              month: index + 1,
              monthName: new Date(year, index).toLocaleString('en-US', { month: 'long' }),
              totalOrders: 0,
              totalRevenue: 0
            })),
            totalOrdersForYear: 0,
            totalRevenueForYear: 0
          }
        });
      }

      const shopIds = shops.map(shop => shop._id);
      
      // Get all products from user's shops
      const products = await Product.find({ shop: { $in: shopIds } }).select('_id');
      const productIds = products.map(p => p._id);

      // Get orders per month using aggregation
      const ordersPerMonth = await Order.aggregate([
        {
          $match: {
            createdAt: {
              $gte: new Date(year, 0, 1), // Start of year
              $lt: new Date(year + 1, 0, 1) // Start of next year
            },
            'items.product': { $in: productIds }
          }
        },
        {
          $addFields: {
            relevantItems: {
              $filter: {
                input: '$items',
                as: 'item',
                cond: { $in: ['$$item.product', productIds] }
              }
            }
          }
        },
        {
          $match: {
            'relevantItems.0': { $exists: true }
          }
        },
        {
          $addFields: {
            relevantRevenue: {
              $sum: '$relevantItems.totalPrice'
            }
          }
        },
        {
          $group: {
            _id: { $month: '$createdAt' },
            totalOrders: { $sum: 1 },
            totalRevenue: { $sum: '$relevantRevenue' }
          }
        },
        {
          $sort: { '_id': 1 }
        }
      ]);

      // Create array with all 12 months, filling missing months with 0
      const monthlyData = Array.from({ length: 12 }, (_, index) => {
        const monthNumber = index + 1;
        const monthData = ordersPerMonth.find(item => item._id === monthNumber);
        return {
          month: monthNumber,
          monthName: new Date(year, index).toLocaleString('en-US', { month: 'long' }),
          totalOrders: monthData ? monthData.totalOrders : 0,
          totalRevenue: monthData ? monthData.totalRevenue : 0
        };
      });

      res.status(200).json({
        success: true,
        message: `Orders per month for ${year}`,
        data: {
          year,
          monthlyData,
          totalOrdersForYear: monthlyData.reduce((sum, month) => sum + month.totalOrders, 0),
          totalRevenueForYear: monthlyData.reduce((sum, month) => sum + month.totalRevenue, 0)
        }
      });

    } catch (error: any) {
      console.error('Get orders per month error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get orders per month',
        error: error.message
    });
  }
}

export const getTotalRevenue = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;

      // Find all shops owned by this business owner
      const shops = await Shop.find({ userId: userId });
      if (shops.length === 0) {
        return res.status(200).json({
          success: true,
          message: 'Total revenue retrieved successfully',
          data: {
            totalRevenue: 0,
            totalOrders: 0,
            averageOrderValue: 0,
            revenueByStatus: [],
            shops: []
          }
        });
      }

      const shopIds = shops.map(shop => shop._id);
      
      // Get all products from user's shops
      const products = await Product.find({ shop: { $in: shopIds } }).select('_id');
      const productIds = products.map(p => p._id);

      // Get total revenue using aggregation
      const revenueStats = await Order.aggregate([
        {
          $match: {
            'items.product': { $in: productIds },
            orderStatus: { $ne: OrderStatus.CANCELLED }
          }
        },
        {
          $addFields: {
            relevantItems: {
              $filter: {
                input: '$items',
                as: 'item',
                cond: { $in: ['$$item.product', productIds] }
              }
            }
          }
        },
        {
          $match: {
            'relevantItems.0': { $exists: true }
          }
        },
        {
          $addFields: {
            relevantRevenue: {
              $sum: '$relevantItems.totalPrice'
            }
          }
        },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$relevantRevenue' },
            totalOrders: { $sum: 1 },
            averageOrderValue: { $avg: '$relevantRevenue' }
          }
        }
      ]);

      const result = revenueStats[0] || {
        totalRevenue: 0,
        totalOrders: 0,
        averageOrderValue: 0
      };

      // Get revenue by order status
      const revenueByStatus = await Order.aggregate([
        {
          $match: {
            'items.product': { $in: productIds }
          }
        },
        {
          $addFields: {
            relevantItems: {
              $filter: {
                input: '$items',
                as: 'item',
                cond: { $in: ['$$item.product', productIds] }
              }
            }
          }
        },
        {
          $match: {
            'relevantItems.0': { $exists: true }
          }
        },
        {
          $addFields: {
            relevantRevenue: {
              $sum: '$relevantItems.totalPrice'
            }
          }
        },
        {
          $group: {
            _id: '$orderStatus',
            revenue: { $sum: '$relevantRevenue' },
            count: { $sum: 1 }
          }
        }
      ]);

      res.status(200).json({
        success: true,
        message: 'Total revenue retrieved successfully',
        data: {
          ...result,
          revenueByStatus: revenueByStatus.map((status: any) => ({
            status: status._id,
            revenue: status.revenue,
            orderCount: status.count
          })),
          shops: shops.map((shop: any) => ({
            shopId: shop._id,
            shopName: shop.shopName
          }))
        }
      });

    } catch (error: any) {
      console.error('Get total revenue error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get total revenue',
        error: error.message
      });
    }
};

export const getRevenuePerMonth = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;
      const year = parseInt(req.query.year as string) || new Date().getFullYear();

      // Find all shops owned by this business owner
      const shops = await Shop.find({ userId: userId });
      if (shops.length === 0) {
        return res.status(200).json({
          success: true,
          message: `Revenue per month for ${year}`,
          data: {
            year,
            monthlyRevenueData: Array.from({ length: 12 }, (_, index) => ({
              month: index + 1,
              monthName: new Date(year, index).toLocaleString('en-US', { month: 'long' }),
              totalRevenue: 0,
              totalOrders: 0,
              averageOrderValue: 0,
              deliveredRevenue: 0,
              pendingRevenue: 0
            })),
            yearlyTotals: {
              totalRevenue: 0,
              totalOrders: 0,
              averageOrderValue: 0,
              deliveredRevenue: 0,
              pendingRevenue: 0
            },
            shops: []
          }
        });
      }

      const shopIds = shops.map(shop => shop._id);
      
      // Get all products from user's shops
      const products = await Product.find({ shop: { $in: shopIds } }).select('_id');
      const productIds = products.map(p => p._id);

      // Get revenue per month using aggregation
      const revenuePerMonth = await Order.aggregate([
        {
          $match: {
            createdAt: {
              $gte: new Date(year, 0, 1), // Start of year
              $lt: new Date(year + 1, 0, 1) // Start of next year
            },
            orderStatus: { $ne: OrderStatus.CANCELLED },
            'items.product': { $in: productIds }
          }
        },
        {
          $addFields: {
            relevantItems: {
              $filter: {
                input: '$items',
                as: 'item',
                cond: { $in: ['$$item.product', productIds] }
              }
            }
          }
        },
        {
          $match: {
            'relevantItems.0': { $exists: true }
          }
        },
        {
          $addFields: {
            relevantRevenue: {
              $sum: '$relevantItems.totalPrice'
            }
          }
        },
        {
          $group: {
            _id: { $month: '$createdAt' },
            totalRevenue: { $sum: '$relevantRevenue' },
            totalOrders: { $sum: 1 },
            averageOrderValue: { $avg: '$relevantRevenue' },
            deliveredRevenue: {
              $sum: {
                $cond: [
                  { $eq: ['$orderStatus', OrderStatus.DELIVERED] },
                  '$relevantRevenue',
                  0
                ]
              }
            },
            pendingRevenue: {
              $sum: {
                $cond: [
                  { $eq: ['$orderStatus', OrderStatus.PENDING] },
                  '$relevantRevenue',
                  0
                ]
              }
            }
          }
        },
        {
          $sort: { '_id': 1 }
        }
      ]);

      // Create array with all 12 months, filling missing months with 0
      const monthlyRevenueData = Array.from({ length: 12 }, (_, index) => {
        const monthNumber = index + 1;
        const monthData = revenuePerMonth.find(item => item._id === monthNumber);
        return {
          month: monthNumber,
          monthName: new Date(year, index).toLocaleString('en-US', { month: 'long' }),
          totalRevenue: monthData ? Math.round(monthData.totalRevenue * 100) / 100 : 0,
          totalOrders: monthData ? monthData.totalOrders : 0,
          averageOrderValue: monthData ? Math.round(monthData.averageOrderValue * 100) / 100 : 0,
          deliveredRevenue: monthData ? Math.round(monthData.deliveredRevenue * 100) / 100 : 0,
          pendingRevenue: monthData ? Math.round(monthData.pendingRevenue * 100) / 100 : 0
        };
      });

      const yearlyTotals = {
        totalRevenue: Math.round(monthlyRevenueData.reduce((sum, month) => sum + month.totalRevenue, 0) * 100) / 100,
        totalOrders: monthlyRevenueData.reduce((sum, month) => sum + month.totalOrders, 0),
        averageOrderValue: Math.round((monthlyRevenueData.reduce((sum, month) => sum + month.totalRevenue, 0) / 
                                     Math.max(monthlyRevenueData.reduce((sum, month) => sum + month.totalOrders, 0), 1)) * 100) / 100,
        deliveredRevenue: Math.round(monthlyRevenueData.reduce((sum, month) => sum + month.deliveredRevenue, 0) * 100) / 100,
        pendingRevenue: Math.round(monthlyRevenueData.reduce((sum, month) => sum + month.pendingRevenue, 0) * 100) / 100
      };

      res.status(200).json({
        success: true,
        message: `Revenue per month for ${year}`,
        data: {
          year,
          monthlyRevenueData,
          yearlyTotals,
          shops: shops.map(shop => ({
            shopId: shop._id,
            shopName: shop.shopName
          }))
        }
      });

    } catch (error: any) {
      console.error('Get revenue per month error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get revenue per month',
        error: error.message
      });
    }
};

