import { Router } from 'express';
import {
  getAllBrandOwners,
  getUnverifiedBrands,
  toggleBrandOwnerVerification,
  getBrandDetails,
  getAllUsers,
  getUserDetails,
  getBrandOwnerShops,
  getBrandOwnerProducts,
  searchCategories,
  searchSubCategories,
  getAllOrders,
  getOrderDetails,
  getAllAdmins,
  getAdminDetails,
  addSubAdmin
} from '../controllers/admin.controller';
import { adminSignup, adminLogin } from '../controllers/auth.controller';
import { getAllProductsAdmin } from '../controllers/product.controller';
import { verifyTokenForAdmin } from '../middlewares/auth.middlewares';

const router = Router();

// Public admin routes
router.post('/login', adminLogin);

// Protected admin routes - require admin authentication
router.use(verifyTokenForAdmin);

router.post('/signup', adminSignup);

// Product management
router.get('/products', getAllProductsAdmin);

// Brand owner management
router.get('/brand-owners', getAllBrandOwners);
router.get('/unverified-brands', getUnverifiedBrands);
router.put('/brand-owners/:brandOwnerId/toggle-verification', toggleBrandOwnerVerification);
router.get('/brand-owners/:brandOwnerId/details', getBrandDetails);
router.get('/brand-owners/:brandOwnerId/shops', getBrandOwnerShops);
router.get('/brand-owners/:brandOwnerId/products', getBrandOwnerProducts);

// User management
router.get('/users', getAllUsers);
router.get('/users/:userId/details', getUserDetails);

// Category management
router.get('/categories/search', searchCategories);

// Subcategory management
router.get('/subcategories/search', searchSubCategories);

// Order management
router.get('/orders', getAllOrders);
router.get('/orders/:orderId/details', getOrderDetails);

// Admin management
router.get('/admins', getAllAdmins);
router.get('/admins/:adminId/details', getAdminDetails);
router.post('/admins', addSubAdmin);

export default router;
