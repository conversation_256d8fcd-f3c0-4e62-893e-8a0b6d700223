import mongoose, { Schema, Document } from 'mongoose';
import { ICustomProduct } from '../interfaces/customProduct.interfaces';

interface ICustomProductDocument extends Omit<ICustomProduct, '_id'>, Document {}

const CustomizationSchema = new Schema({
  customWriting: {
    text: { type: String, maxlength: 50 },
    alignment: { 
      type: String, 
      enum: ['left', 'center', 'right'],
      default: 'center'
    },
    characterLimit: { type: Number, default: 50 }
  },
  style: { type: String, required: true },
  color: { type: String, required: true },
  size: { type: String, required: true }
});

const CustomProductSchema = new Schema({
  baseProductId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Product', 
    required: true 
  },
  userId: { 
    type: Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  customization: { 
    type: CustomizationSchema, 
    required: true 
  },
  price: { type: Number, required: true },
  quantity: { 
    type: Number, 
    required: true, 
    min: 1,
    default: 1 
  }
}, {
  timestamps: true
});

// Index for efficient querying
CustomProductSchema.index({ userId: 1, baseProductId: 1 });
CustomProductSchema.index({ createdAt: -1 });

export default mongoose.model<ICustomProductDocument>('CustomProduct', CustomProductSchema);
