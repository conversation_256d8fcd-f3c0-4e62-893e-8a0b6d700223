import { Request, Response } from 'express';
import Order from '../models/Order';
import Product from '../models/Product';
import { IUpdateOrderStatusRequest, OrderStatus, PaymentStatus } from '../interfaces/order.interfaces';



export const getUserOrders = async (req: Request, res: Response) =>{
    try {
      const userId = req.user!._id;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const skip = (page - 1) * limit;

      const orders = await Order.find({ user: userId })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate({
          path: 'items.product',
          select: 'productName productMedia'
        })
        .populate({
          path: 'deliveryAddress',
          populate: {
            path: 'user',
            select: 'fullname phoneNumber'
          }
        });

      const totalOrders = await Order.countDocuments({ user: userId });
      const totalPages = Math.ceil(totalOrders / limit);

      res.status(200).json({
        success: true,
        message: 'Orders retrieved successfully',
        data: {
          orders,
          pagination: {
            currentPage: page,
            totalPages,
            totalOrders,
            hasNext: page < totalPages,
            hasPrev: page > 1
          }
        }
      });

    } catch (error: any) {
      console.error('Get user orders error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve orders',
        error: error.message
      });
    }
};


export const getOrder = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;
      const { orderId } = req.params;

      const order = await Order.findOne({ 
        _id: orderId, 
        user: userId 
      }).populate({
        path: 'items.product',
        select: 'productName productMedia'
      }).populate({
        path: 'deliveryAddress',
        populate: {
          path: 'user',
          select: 'fullname phoneNumber'
        }
      });

      if (!order) {
        return res.status(404).json({
          success: false,
          message: 'Order not found'
        });
      }

      res.status(200).json({
        success: true,
        message: 'Order retrieved successfully',
        data: order
      });

    } catch (error: any) {
      console.error('Get order error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve order',
        error: error.message
      });
    }
};

export const getOrderByNumber = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;
      const { orderNumber } = req.params;

      const order = await Order.findOne({ 
        orderNumber, 
        user: userId 
      }).populate({
        path: 'items.product',
        select: 'productName productMedia'
      }).populate({
        path: 'deliveryAddress',
        populate: {
          path: 'user',
          select: 'fullname phoneNumber'
        }
      });

      if (!order) {
        return res.status(404).json({
          success: false,
          message: 'Order not found'
        });
      }

      res.status(200).json({
        success: true,
        message: 'Order retrieved successfully',
        data: order
      });

    } catch (error: any) {
      console.error('Get order by number error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve order',
        error: error.message
      });
    }
};

export const cancelOrder = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;
      const { orderId } = req.params;

      const order = await Order.findOne({ 
        _id: orderId, 
        user: userId 
      });

      if (!order) {
        return res.status(404).json({
          success: false,
          message: 'Order not found'
        });
      }

      // Check if order can be cancelled
      if (![OrderStatus.PENDING].includes(order.orderStatus)) {
        return res.status(400).json({
          success: false,
          message: 'Order cannot be cancelled at this stage'
        });
      }

      // Update order status
      order.orderStatus = OrderStatus.CANCELLED;
      await order.save();

      // Restore product stock quantities
      for (const item of order.items) {
        await Product.findByIdAndUpdate(
          item.product,
          { 
            $inc: { 
              stockQuantity: item.quantity,
              stockSold: -item.quantity
            }
          }
        );
      }

      res.status(200).json({
        success: true,
        message: 'Order cancelled successfully',
        data: order
      });

    } catch (error: any) {
      console.error('Cancel order error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to cancel order',
        error: error.message
      });
    }
    
};