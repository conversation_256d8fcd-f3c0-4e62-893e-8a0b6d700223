import dotenv from 'dotenv';
import express from 'express';
import { createServer } from 'http';
import connectDB from './config/db';
import cors from "cors";
import bodyParser from "body-parser";
import helmet from "helmet";
import { sendWelcomeEmail } from './utils/testsmtp';
import { Request, Response } from 'express';
// import { sendOtpEmail } from './utils/emailjs';
//import { rateLimiter } from "./utils/rateLimit";
import { StatusCodes } from "http-status-codes";
import {
  authRoutes,
  shopRoutes,
  categoryRoutes,
  subcategoryRoutes,
  productRoutes,
  adminRoutes,
  wishlistRoutes,
  userRoutes,
  brandOwnerRoutes,
  customProductRoutes,
  cartRoutes,
  orderRoutes,
  businessOrderRoutes,
  checkoutRoutes,
  addressRoutes,
  notificationRoutes
} from './routes/index'

// ----------- Config -------------------
dotenv.config();

// ----------- Server -------------------
const app = express();
const server = createServer(app);
const port = process.env.PORT || 5001;
// app.set('trust proxy', 2);
// // app.enable('trust proxy');
// console.log('Trust proxy set to:', app.get('trust proxy'));

// ---------- Middlewares ----------------------------
app.use(bodyParser.json({ limit: "50mb" }));
app.use(bodyParser.urlencoded({ limit: "50mb", extended: true }));
app.use(cors());
app.use(helmet());
//app.use(rateLimiter());


// ---------- Routes ----------------------------
app.use('/api/auth', authRoutes);
app.use('/api/shops', shopRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/subcategories', subcategoryRoutes);
app.use('/api/products', productRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/wishlist', wishlistRoutes);
app.use('/api/users', userRoutes);
app.use('/api/brand-owners', brandOwnerRoutes);
app.use('/api/custom-products', customProductRoutes);
app.use('/api/cart', cartRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/business/orders', businessOrderRoutes);
app.use('/api/checkout', checkoutRoutes);
app.use('/api/addresses', addressRoutes);
app.use('/api/notifications', notificationRoutes);
// app.post('/send-welcome-email', (req: Request, res: Response) => {
//   // Extract email address from the request body
//   const { email } = req.body;

//   if (!email) {
//     return res.status(400).json({ message: 'Email is required' });
//   }

//   // Send the welcome email
//   sendOtpEmail(email, '1234', '2:00 PM');

//   // Respond to the client
//   return res.status(200).json({ message: 'Welcome email sent!' });
// });



// Connect to MongoDB
connectDB();

// Start server
server.listen(port, () => {
  console.log('Server is running on port ', port);
});
