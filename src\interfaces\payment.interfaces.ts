// Payment Method Types
export enum PaymentMethodType {
  CARD = 'card',
  STRIPE = 'stripe',
  PAYPAL = 'paypal',
  CASH_ON_DELIVERY = 'cash_on_delivery'
}

// Payment Interfaces
export interface IPaymentDetails {
  paymentMethod: PaymentMethodType;
  cardHolderName?: string;
  cardNumber?: string; // This will be tokenized in production
  cvv?: string; // This will not be stored in production
  expiryMonth?: string;
  expiryYear?: string;
  stripePaymentIntentId?: string;
  paypalTransactionId?: string;
}

export interface IPromoCode {
  code: string;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  isActive: boolean;
  validFrom: Date;
  validTo: Date;
  minimumOrderAmount?: number;
  maxUsageCount?: number;
  currentUsageCount: number;
}

// Checkout Request Interfaces
export interface ICheckoutRequest {
  shippingAddressId?: string; // Optional - uses default address if not provided
  paymentDetails: IPaymentDetails;
  promoCode?: string;
  notes?: string;
}

export interface IAddAddressRequest {
  streetAddress: string;
  apartment?: string;
  state: string;
  isDefault?: boolean;
}

export interface IApplyPromoCodeRequest {
  promoCode: string;
}

export interface ICheckoutSummary {
  items: any[];
  subtotal: number;
  shippingCost: number;
  tax: number;
  discountAmount: number;
  promoCode?: string;
  totalAmount: number;
  unavailableItems?: any[];
}
