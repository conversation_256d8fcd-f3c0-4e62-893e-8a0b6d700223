// // src/config/passport.ts
// import passport from 'passport';
// import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
// import User from '../models/User';
// import { IUser } from '../interfaces/user.interfaces';
// // config env 
// import dotenv from 'dotenv';
// dotenv.config();

// const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
// const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET;
// console.log('Google Client ID:', GOOGLE_CLIENT_ID);
// console.log('Google Client Secret:', GOOGLE_CLIENT_SECRET);

// if (!GOOGLE_CLIENT_ID || !GOOGLE_CLIENT_SECRET) {
//   throw new Error('Google OAuth credentials are not defined in environment variables');
// }

// // Serialize user for session
// passport.serializeUser((user: any, done) => {
//   done(null, user._id);
// });

// // Deserialize user from session
// passport.deserializeUser(async (id: string, done) => {
//   try {
//     const user = await User.findById(id);
//     done(null, user);
//   } catch (error) {
//     done(error, null);
//   }
// });

// // Google OAuth Strategy
// passport.use(
//   new GoogleStrategy(
//     {
//       clientID: GOOGLE_CLIENT_ID,
//       clientSecret: GOOGLE_CLIENT_SECRET,
//       callbackURL: '/api/auth/google/callback',
//     },
//     async (accessToken, refreshToken, profile, done) => {
//       try {
//         console.log('Google Profile:', profile);

//         // Check if user already exists with this Google ID
//         let existingUser = await User.findOne({ googleId: profile.id });

//         if (existingUser) {
//           // User exists with this Google ID
//           return done(null, existingUser);
//         }

//         // Check if user exists with this email (for linking accounts)
//         existingUser = await User.findOne({ email: profile.emails?.[0]?.value });

//         if (existingUser) {
//           // Link Google account to existing user
//           existingUser.googleId = profile.id;
//           existingUser.authProvider = 'google';
//           existingUser.isGoogleUser = true;
//           existingUser.isEmailVerified = true; // Google emails are verified
//           if (profile.photos?.[0]?.value) {
//             existingUser.ProfilePicture = profile.photos[0].value;
//           }
//           await existingUser.save();
//           return done(null, existingUser);
//         }

//         // Create new user
//         const newUser = new User({
//           googleId: profile.id,
//           fullname: profile.displayName || `${profile.name?.givenName || ''} ${profile.name?.familyName || ''}`.trim(),
//           email: profile.emails?.[0]?.value,
//           ProfilePicture: profile.photos?.[0]?.value || "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS2TgOv9CMmsUzYKCcLGWPvqcpUk6HXp2mnww&s",
//           role: 'user', // Default role
//           authProvider: 'google',
//           isGoogleUser: true,
//           isEmailVerified: true, // Google emails are verified
//           isBrandOwnerVerified: false,
//           isBrandOwner: false,
//         });

//         await newUser.save();
//         return done(null, newUser);
//       } catch (error) {
//         console.error('Error in Google OAuth strategy:', error);
//         return done(error, false);
//       }
//     }
//   )
// );

// export default passport;