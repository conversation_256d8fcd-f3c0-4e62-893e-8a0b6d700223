# API Endpoints Summary - New Features Added

## User Schema Updates
Added new fields to User model and interface:
- `brandName`: string (optional, for business owners)
- `address`: string (optional)
- `websitelink`: string (optional, validated URL)
- `bio`: string (optional, max 1000 chars)
- `ProfilePicture`: string (existing field, now properly used)

## New API Endpoints

### User Controller (`/api/users`)
All routes require user authentication (`verifyToken`)

1. **PUT /api/users/update-info** - Update user information
   - Body: `{ fullname?, phoneNumber? }`
   - Files: `profilePicture` (optional, uploaded to S3)
   - Updates: fullname, phoneNumber, profilePicture

2. **DELETE /api/users/delete-profile** - Delete user profile
   - Completely removes user account

3. **GET /api/users/info** - Get user information
   - Returns: fullname, phoneNumber, ProfilePicture, email, address

4. **PUT /api/users/update-password** - Update user password
   - Body: `{ oldPassword, newPassword, confirmPassword }`
   - Validates old password and updates to new password

### Brand Owner Controller (`/api/brand-owners`)

#### Public Routes:
1. **POST /api/brand-owners/signup** - Brand owner signup
   - Body: `{ role, brandName, email, phone, address, websitelink?, bio?, password, confirmPassword }`
   - Files: `profilePicture` (optional, uploaded to S3)
   - Creates brand owner account with email verification

#### Protected Routes (require `verifyTokenForbrandOwner`):
2. **PUT /api/brand-owners/update-info** - Update brand owner info
   - Body: `{ brandName?, address?, websitelink?, bio? }`
   - Files: `profilePicture` (optional)
   - Updates brand information

3. **PUT /api/brand-owners/update-password** - Update brand owner password
   - Body: `{ oldPassword, newPassword, confirmPassword }`
   - Same validation as user password update

4. **DELETE /api/brand-owners/delete-brand** - Delete brand
   - Removes brand owner account completely

5. **GET /api/brand-owners/info** - Get brand information
   - Returns: brandName, address, websitelink, bio, ProfilePicture, email, phoneNumber

### Admin Controller (`/api/admin`)
All protected routes require admin authentication (`verifyTokenForAdmin`)

1. **GET /api/admin/brand-owners** - Get all brand owners
   - Returns list of all business owners with their information

2. **GET /api/admin/unverified-brands** - Get unverified brands
   - Returns brands where `isBrandOwnerVerified: false`

3. **PUT /api/admin/brand-owners/:brandOwnerId/toggle-verification** - Toggle verification
   - Toggles `isBrandOwnerVerified` status (true ↔ false)

4. **GET /api/admin/brand-owners/:brandOwnerId/details** - Get brand details
   - Returns complete brand owner information

5. **GET /api/admin/users** - Get all users
   - Returns list of all users (role: 'user')

## Authentication & Validation

### Media Upload
- All profile picture uploads use existing S3 integration
- Uses `validateMedia` middleware for file validation
- Supports image files up to 50MB

### Password Security
- Minimum 6 characters
- Bcrypt hashing with salt rounds: 12
- Old password verification required for updates

### Input Validation
- Email format validation
- Phone number format validation
- Website URL validation (must start with http/https)
- Field length limits enforced
- Trim whitespace from text inputs

## Error Handling
- Consistent HTTP status codes
- Detailed error messages
- Input validation errors
- Authentication/authorization errors
- File upload errors

## Database Changes
- Updated User schema with new optional fields
- Fixed typo: `isbrandOwnerVerfied` → `isBrandOwnerVerified`
- All new fields are optional to maintain backward compatibility

## Route Registration
Updated `src/server.ts` to include:
- `/api/users` → userRoutes
- `/api/brand-owners` → brandOwnerRoutes
- Enhanced `/api/admin` → adminRoutes (with new endpoints)
