import { Document, Types } from 'mongoose';
import { ProductSize } from './product.interfaces';
import { IPaymentDetails } from './payment.interfaces';

export enum OrderStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded'
}

export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
  REFUNDED = 'refunded'
}

export interface IOrderItem {
  product: Types.ObjectId;
  productName: string; // Store product name at time of order
  quantity: number;
  size: ProductSize;
  color: string;
  priceAtTime: number;
  totalPrice: number;
}

export interface IOrder extends Document {
  _id: Types.ObjectId;
  user: Types.ObjectId;
  orderNumber: string; // Unique order number like "56928"
  items: IOrderItem[];
  deliveryAddress: Types.ObjectId; // Reference to DeliveryAddress model
  subtotal: number;
  shippingCost: number;
  tax?: number;
  totalAmount: number;
  orderStatus: OrderStatus;
  paymentStatus: PaymentStatus;
  paymentMethod?: string;
  paymentDetails?: Partial<IPaymentDetails>; // Store safe payment details
  promoCode?: string;
  discountAmount?: number;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IUpdateOrderStatusRequest {
  orderStatus?: OrderStatus;
  paymentStatus?: PaymentStatus;
}