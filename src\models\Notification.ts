import mongoose, { Schema } from 'mongoose';
import { INotification, NotificationType } from '../interfaces/notification.interfaces';

const NotificationSchema: Schema = new Schema<INotification>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required'],
    index: true
  },
  heading: {
    type: String,
    required: [true, 'Notification heading is required'],
    trim: true,
    maxlength: [200, 'Heading cannot exceed 200 characters'],
    minlength: [1, 'Heading must be at least 1 character']
  },
  description: {
    type: String,
    required: [true, 'Notification description is required'],
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters'],
    minlength: [1, 'Description must be at least 1 character']
  },
  isRead: {
    type: Boolean,
    default: false,
    index: true
  },
  orderId: {
    type: Schema.Types.ObjectId,
    ref: 'Order',
    required: false,
    index: true
  },
  productId: {
    type: Schema.Types.ObjectId,
    ref: 'Product',
    required: false,
    index: true
  },
  categoryId: {
    type: Schema.Types.ObjectId,
    ref: 'Category',
    required: false,
    index: true
  },
  subcategoryId: {
    type: Schema.Types.ObjectId,
    ref: 'SubCategory',
    required: false,
    index: true
  },
  notificationType: {
    type: String,
    enum: Object.values(NotificationType),
    required: [true, 'Notification type is required'],
    index: true
  }
}, {
  timestamps: true
});

// Compound indexes for efficient queries
NotificationSchema.index({ userId: 1, isRead: 1 });
NotificationSchema.index({ userId: 1, createdAt: -1 });
NotificationSchema.index({ userId: 1, notificationType: 1 });
NotificationSchema.index({ userId: 1, isRead: 1, createdAt: -1 });

// Index for cleanup operations (optional - for future use)
NotificationSchema.index({ createdAt: 1 }, { expireAfterSeconds: 2592000 }); // 30 days TTL

export default mongoose.model<INotification>('Notification', NotificationSchema);
