import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { Types } from 'mongoose';
import User from '../models/User';

// Get all brand owners along with info
export const getAllBrandOwners = async (req: Request, res: Response) => {
  try {
    const brandOwners = await User.find({ role: 'brandOwner' })
      .select('-password -otp -otpCreatedAt')
      .sort({ createdAt: -1 });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Brand owners retrieved successfully',
      count: brandOwners.length,
      brandOwners: brandOwners.map(owner => ({
        id: owner._id,
        email: owner.email,
        brandName: owner.brandName,
        address: owner.address,
        websitelink: owner.websitelink,
        bio: owner.bio,
        phoneNumber: owner.phoneNumber,
        ProfilePicture: owner.ProfilePicture,
        isEmailVerified: owner.isEmailVerified,
        isBrandOwnerVerified: owner.isBrandOwnerVerified,
        createdAt: owner.createdAt,
        updatedAt: owner.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error getting all brand owners:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving brand owners',
      error
    });
  }
};

// Get all brands whose isBrandOwnerVerified is false
export const getUnverifiedBrands = async (req: Request, res: Response) => {
  try {
    const unverifiedBrands = await User.find({
      role: 'brandOwner',
      isBrandOwnerVerified: false
    })
      .select('-password -otp -otpCreatedAt')
      .sort({ createdAt: -1 });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Unverified brands retrieved successfully',
      count: unverifiedBrands.length,
      unverifiedBrands: unverifiedBrands.map(brand => ({
        id: brand._id,
        email: brand.email,
        brandName: brand.brandName,
        address: brand.address,
        websitelink: brand.websitelink,
        bio: brand.bio,
        phoneNumber: brand.phoneNumber,
        ProfilePicture: brand.ProfilePicture,
        isEmailVerified: brand.isEmailVerified,
        isBrandOwnerVerified: brand.isBrandOwnerVerified,
        createdAt: brand.createdAt,
        updatedAt: brand.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error getting unverified brands:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving unverified brands',
      error
    });
  }
};

// Toggle brand owner verification status
export const toggleBrandOwnerVerification = async (req: Request, res: Response) => {
  try {
    const { brandOwnerId } = req.params;

    if (!brandOwnerId || !Types.ObjectId.isValid(brandOwnerId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Valid brand owner ID is required'
      });
    }

    const brandOwner = await User.findById(brandOwnerId);
    if (!brandOwner) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Brand owner not found'
      });
    }

    if (brandOwner.role !== 'brandOwner') {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'User is not a Brand owner'
      });
    }

    // Toggle verification status
    const newVerificationStatus = !brandOwner.isBrandOwnerVerified;

    const updatedBrandOwner = await User.findByIdAndUpdate(
      brandOwnerId,
      { isBrandOwnerVerified: newVerificationStatus },
      { new: true }
    ).select('-password -otp -otpCreatedAt');

    return res.status(StatusCodes.OK).json({
      success: true,
      message: `Brand owner verification status ${newVerificationStatus ? 'enabled' : 'disabled'} successfully`,
      brandOwner: {
        id: updatedBrandOwner?._id,
        email: updatedBrandOwner?.email,
        brandName: updatedBrandOwner?.brandName,
        isBrandOwnerVerified: updatedBrandOwner?.isBrandOwnerVerified
      }
    });

  } catch (error) {
    console.error('Error toggling brand owner verification:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error updating brand owner verification status',
      error
    });
  }
};

// Get all info of a brand (all info used to signup)
export const getBrandDetails = async (req: Request, res: Response) => {
  try {
    const { brandOwnerId } = req.params;

    if (!brandOwnerId || !Types.ObjectId.isValid(brandOwnerId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Valid brand owner ID is required'
      });
    }

    const brandOwner = await User.findById(brandOwnerId).select('-password -otp -otpCreatedAt');
    if (!brandOwner) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Brand owner not found'
      });
    }

    if (brandOwner.role !== 'brandOwner') {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'User is not a business owner'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Brand details retrieved successfully',
      brand: {
        id: brandOwner._id,
        email: brandOwner.email,
        brandName: brandOwner.brandName,
        phoneNumber: brandOwner.phoneNumber,
        address: brandOwner.address,
        websitelink: brandOwner.websitelink,
        bio: brandOwner.bio,
        ProfilePicture: brandOwner.ProfilePicture,
        role: brandOwner.role,
        isEmailVerified: brandOwner.isEmailVerified,
        isBrandOwnerVerified: brandOwner.isBrandOwnerVerified,
        createdAt: brandOwner.createdAt,
        updatedAt: brandOwner.updatedAt
      }
    });

  } catch (error) {
    console.error('Error getting brand details:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving brand details',
      error
    });
  }
};

// Get all users
export const getAllUsers = async (req: Request, res: Response) => {
  try {
    const users = await User.find({ role: 'user' })
      .select('-password -otp -otpCreatedAt')
      .sort({ createdAt: -1 });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Users retrieved successfully',
      count: users.length,
      users: users.map(user => ({
        id: user._id,
        email: user.email,
        fullname: user.fullname,
        phoneNumber: user.phoneNumber,
        ProfilePicture: user.ProfilePicture,
        address: user.address,
        role: user.role,
        isEmailVerified: user.isEmailVerified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error getting all users:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving users',
      error
    });
  }
};

// Get specific user details
export const getUserDetails = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;

    if (!userId || !Types.ObjectId.isValid(userId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Valid user ID is required'
      });
    }

    const user = await User.findById(userId).select('-password -otp -otpCreatedAt');
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'User not found'
      });
    }

    if (user.role !== 'user') {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'This is not a regular user'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'User details retrieved successfully',
      user: {
        id: user._id,
        email: user.email,
        fullname: user.fullname,
        phoneNumber: user.phoneNumber,
        ProfilePicture: user.ProfilePicture,
        address: user.address,
        role: user.role,
        isEmailVerified: user.isEmailVerified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });

  } catch (error) {
    console.error('Error getting user details:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving user details',
      error
    });
  }
};

// Get brand owner shops
export const getBrandOwnerShops = async (req: Request, res: Response) => {
  try {
    const { brandOwnerId } = req.params;

    if (!brandOwnerId || !Types.ObjectId.isValid(brandOwnerId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Valid brand owner ID is required'
      });
    }

    const brandOwner = await User.findById(brandOwnerId);
    if (!brandOwner) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Brand owner not found'
      });
    }

    if (brandOwner.role !== 'brandOwner') {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'User is not a brand owner'
      });
    }

    // Import Shop model dynamically to avoid circular dependency
    const Shop = (await import('../models/Shop')).default;
    
    const shops = await Shop.find({ userId: new Types.ObjectId(brandOwnerId) })
      .populate('userId', 'fullname email brandName')
      .sort({ createdAt: -1 })
      .select('-__v');

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Brand owner shops retrieved successfully',
      count: shops.length,
      brandOwner: {
        id: brandOwner._id,
        email: brandOwner.email,
        brandName: brandOwner.brandName
      },
      shops: shops.map(shop => ({
        id: shop._id,
        shopName: shop.shopName,
        shopImage: shop.shopImage,
        noOfProducts: shop.noOfProducts,
        createdAt: shop.createdAt,
        updatedAt: shop.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error getting brand owner shops:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving brand owner shops',
      error
    });
  }
};

// Get brand owner products
export const getBrandOwnerProducts = async (req: Request, res: Response) => {
  try {
    const { brandOwnerId } = req.params;
    const { shopId } = req.query;

    if (!brandOwnerId || !Types.ObjectId.isValid(brandOwnerId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Valid brand owner ID is required'
      });
    }

    const brandOwner = await User.findById(brandOwnerId);
    if (!brandOwner) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Brand owner not found'
      });
    }

    if (brandOwner.role !== 'brandOwner') {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'User is not a brand owner'
      });
    }

    // Import models dynamically
    const Shop = (await import('../models/Shop')).default;
    const Product = (await import('../models/Product')).default;

    // Build filter for products
    const productFilter: any = {};
    
    if (shopId && Types.ObjectId.isValid(shopId as string)) {
      // Verify shop belongs to brand owner
      const shop = await Shop.findOne({
        _id: new Types.ObjectId(shopId as string),
        userId: new Types.ObjectId(brandOwnerId)
      });
      
      if (!shop) {
        return res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: 'Shop not found or does not belong to this brand owner'
        });
      }
      
      productFilter.shop = new Types.ObjectId(shopId as string);
    } else {
      // Get all shops of the brand owner
      const shops = await Shop.find({ userId: new Types.ObjectId(brandOwnerId) });
      const shopIds = shops.map(shop => shop._id);
      productFilter.shop = { $in: shopIds };
    }

    const products = await Product.find(productFilter)
      .populate('shop', 'shopName')
      .populate('productCategory', 'categoryName')
      .populate('productSubCategory', 'subCategoryName')
      .sort({ createdAt: -1 })
      .select('-__v');

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Brand owner products retrieved successfully',
      count: products.length,
      brandOwner: {
        id: brandOwner._id,
        email: brandOwner.email,
        brandName: brandOwner.brandName
      },
      products: products.map(product => ({
        id: product._id,
        productName: product.productName,
        productMedia: product.productMedia,
        price: product.price,
        stockQuantity: product.stockQuantity,
        stockSold: product.stockSold,
        shop: product.shop,
        productCategory: product.productCategory,
        productSubCategory: product.productSubCategory,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error getting brand owner products:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving brand owner products',
      error
    });
  }
};

// Search categories
export const searchCategories = async (req: Request, res: Response) => {
  try {
    const { search, categoryType } = req.query;

    if (!search || typeof search !== 'string') {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Search query is required'
      });
    }

    // Import Category model dynamically
    const Category = (await import('../models/Category')).default;

    // Build filter object
    const filter: any = {
      categoryName: { $regex: search, $options: 'i' }
    };

    if (categoryType && typeof categoryType === 'string') {
      filter.categoryType = categoryType.toLowerCase();
    }

    const categories = await Category.find(filter)
      .sort({ categoryName: 1 })
      .select('-__v');

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Categories search completed successfully',
      count: categories.length,
      searchQuery: search,
      categories: categories.map(category => ({
        id: category._id,
        categoryImage: category.categoryImage,
        categoryName: category.categoryName,
        categoryDescription: category.categoryDescription,
        categoryType: category.categoryType,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error searching categories:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error searching categories',
      error
    });
  }
};

// Search subcategories
export const searchSubCategories = async (req: Request, res: Response) => {
  try {
    const { search, categoryId } = req.query;

    if (!search || typeof search !== 'string') {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Search query is required'
      });
    }

    // Import SubCategory model dynamically
    const SubCategory = (await import('../models/SubCategory')).default;

    // Build filter object
    const filter: any = {
      subCategoryName: { $regex: search, $options: 'i' }
    };

    if (categoryId && typeof categoryId === 'string') {
      if (!Types.ObjectId.isValid(categoryId)) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: 'Invalid category ID'
        });
      }
      filter.category = new Types.ObjectId(categoryId);
    }

    const subCategories = await SubCategory.find(filter)
      .populate('category', 'categoryName categoryType categoryImage')
      .sort({ subCategoryName: 1 })
      .select('-__v');

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Subcategories search completed successfully',
      count: subCategories.length,
      searchQuery: search,
      subCategories: subCategories.map(subCategory => ({
        id: subCategory._id,
        category: subCategory.category,
        subCategoryName: subCategory.subCategoryName,
        createdAt: subCategory.createdAt,
        updatedAt: subCategory.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error searching subcategories:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error searching subcategories',
      error
    });
  }
};

// Get all orders for admin
export const getAllOrders = async (req: Request, res: Response) => {
  try {
    const { page = 1, limit = 10, status, userId } = req.query;
    const pageNum = parseInt(page as string, 10);
    const limitNum = parseInt(limit as string, 10);
    const skip = (pageNum - 1) * limitNum;

    // Import Order model dynamically
    const Order = (await import('../models/Order')).default;

    // Build filter object
    const filter: any = {};
    if (status && typeof status === 'string') {
      filter.orderStatus = status;
    }
    if (userId && typeof userId === 'string' && Types.ObjectId.isValid(userId)) {
      filter.user = new Types.ObjectId(userId);
    }

    const orders = await Order.find(filter)
      .populate('user', 'fullname email phoneNumber')
      .populate({
        path: 'items.product',
        select: 'productName productMedia productPrice'
      })
      .populate('deliveryAddress')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum)
      .select('-__v');

    const totalOrders = await Order.countDocuments(filter);

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Orders retrieved successfully',
      pagination: {
        currentPage: pageNum,
        totalPages: Math.ceil(totalOrders / limitNum),
        totalOrders,
        hasNextPage: pageNum < Math.ceil(totalOrders / limitNum),
        hasPrevPage: pageNum > 1
      },
      orders: orders.map(order => ({
        id: order._id,
        orderNumber: order.orderNumber,
        user: order.user,
        items: order.items,
        totalAmount: order.totalAmount,
        orderStatus: order.orderStatus,
        paymentStatus: order.paymentStatus,
        deliveryAddress: order.deliveryAddress,
        createdAt: order.createdAt,
        updatedAt: order.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error getting all orders:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving orders',
      error
    });
  }
};

// Get specific order details for admin
export const getOrderDetails = async (req: Request, res: Response) => {
  try {
    const { orderId } = req.params;

    if (!orderId || !Types.ObjectId.isValid(orderId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Valid order ID is required'
      });
    }

    // Import Order model dynamically
    const Order = (await import('../models/Order')).default;

    const order = await Order.findById(orderId)
      .populate('user', 'fullname email phoneNumber ProfilePicture')
      .populate({
        path: 'items.product',
        select: 'productName productMedia productPrice',
        populate: {
          path: 'shop',
          select: 'shopName'
        }
      })
      .populate({
        path: 'deliveryAddress',
        populate: {
          path: 'user',
          select: 'fullname phoneNumber'
        }
      })
      .select('-__v');

    if (!order) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Order not found'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Order details retrieved successfully',
      order: {
        id: order._id,
        orderNumber: order.orderNumber,
        user: order.user,
        items: order.items,
        totalAmount: order.totalAmount,
        orderStatus: order.orderStatus,
        paymentStatus: order.paymentStatus,
        paymentMethod: order.paymentMethod,
        deliveryAddress: order.deliveryAddress,
        createdAt: order.createdAt,
        updatedAt: order.updatedAt
      }
    });

  } catch (error) {
    console.error('Error getting order details:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving order details',
      error
    });
  }
};

// Get specific admin details
export const getAdminDetails = async (req: Request, res: Response) => {
  try {
    const { adminId } = req.params;

    if (!adminId || !Types.ObjectId.isValid(adminId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Valid admin ID is required'
      });
    }

    const admin = await User.findById(adminId).select('-password -otp -otpCreatedAt');
    if (!admin) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Admin not found'
      });
    }

    if (admin.role !== 'admin') {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'This user is not an admin'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Admin details retrieved successfully',
      admin: {
        id: admin._id,
        email: admin.email,
        fullname: admin.fullname,
        phoneNumber: admin.phoneNumber,
        ProfilePicture: admin.ProfilePicture,
        role: admin.role,
        isEmailVerified: admin.isEmailVerified,
        createdAt: admin.createdAt,
        updatedAt: admin.updatedAt
      }
    });

  } catch (error) {
    console.error('Error getting admin details:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving admin details',
      error
    });
  }
};

// Get all admins
export const getAllAdmins = async (req: Request, res: Response) => {
  try {
    const { search } = req.query;

    // Build filter object
    const filter: any = { role: 'admin' };
    if (search && typeof search === 'string') {
      filter.$or = [
        { fullname: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    const admins = await User.find(filter)
      .select('-password -otp -otpCreatedAt')
      .sort({ createdAt: -1 });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Admins retrieved successfully',
      count: admins.length,
      searchQuery: search || null,
      admins: admins.map(admin => ({
        id: admin._id,
        email: admin.email,
        fullname: admin.fullname,
        phoneNumber: admin.phoneNumber,
        ProfilePicture: admin.ProfilePicture,
        role: admin.role,
        isEmailVerified: admin.isEmailVerified,
        createdAt: admin.createdAt,
        updatedAt: admin.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error getting all admins:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving admins',
      error
    });
  }
};

// Add sub admin (same as adminSignup but called by existing admin)
export const addSubAdmin = async (req: Request, res: Response) => {
  try {
    const { email, fullname, phoneNumber, password, confirmPassword } = req.body;

    if(!email){
        return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Email is required' });
    }
    if(!fullname){
        return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Full name is required' });
    }
    if(!phoneNumber){
        return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Phone number is required' });
    }
    if(!password){
        return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Password is required' });
    }
    if(!confirmPassword){
        return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Confirm password is required' });
    }

    if(password !== confirmPassword){
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Passwords do not match' });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(StatusCodes.CONFLICT).json({ message: 'User already exists' });
    }

    const newAdmin = new User({
      email: email.toLowerCase(),
      fullname,
      phoneNumber,
      password,
      role: 'admin',
      isEmailVerified: true,
      isBrandOwnerVerified: false
    });

    await newAdmin.save();

    return res.status(StatusCodes.CREATED).json({
      success: true,
      message: 'Sub admin created successfully',
      admin: {
        id: newAdmin._id,
        fullname: newAdmin.fullname,
        email: newAdmin.email,
        phoneNumber: newAdmin.phoneNumber,
        role: newAdmin.role,
        isEmailVerified: newAdmin.isEmailVerified,
        createdAt: newAdmin.createdAt
      }
    });
  } catch (error) {
    console.error('Error creating sub admin:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error creating sub admin',
      error
    });
  }
};