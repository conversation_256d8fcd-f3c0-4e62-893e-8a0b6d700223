import { Request, Response } from 'express';
import Cart from '../models/Cart';
import Product from '../models/Product';
import { IAddToCartRequest, IUpdateCartItemRequest } from '../interfaces/cart.interfaces';
import { Types } from 'mongoose';


export const getCart  = async (req: Request, res: Response) =>
  {
    try {
      const userId = req.user!._id;

      let cart = await Cart.findOne({ user: userId }).populate({
        path: 'items.product',
        select: 'productName productMedia price sizes colors stockQuantity isActive'
      });

      if (!cart) {
        cart = new Cart({ user: userId, items: [], totalAmount: 0 });
        await cart.save();
      }

      // Filter out inactive products or products that no longer exist
      const validItems = cart.items.filter(item => {
        const product = item.product as any;
        return product && product.isActive;
      });

      if (validItems.length !== cart.items.length) {
        cart.items = validItems;
        await cart.save();
      }

      res.status(200).json({
        success: true,
        message: 'Cart retrieved successfully',
        data: cart
      });

    } catch (error: any) {
      console.error('Get cart error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve cart',
        error: error.message
      });
    }
  };

export const addToCart = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;
      const { productId, quantity, size, color }: IAddToCartRequest = req.body;

      // Validate input
      if (!productId || !quantity || !size || !color) {
        return res.status(400).json({
          success: false,
          message: 'Product ID, quantity, size, and color are required'
        });
      }

      if (quantity < 1) {
        return res.status(400).json({
          success: false,
          message: 'Quantity must be at least 1'
        });
      }

      // Check if product exists and is active
      const product = await Product.findById(productId);
      if (!product || !product.isActive) {
        return res.status(404).json({
          success: false,
          message: 'Product not found or not available'
        });
      }

      // Check if size and color are available
      if (!product.sizes.includes(size)) {
        return res.status(400).json({
          success: false,
          message: 'Selected size is not available for this product'
        });
      }

      if (!product.colors.includes(color)) {
        return res.status(400).json({
          success: false,
          message: 'Selected color is not available for this product'
        });
      }

      // Check stock availability
      if (product.stockQuantity < quantity) {
        return res.status(400).json({
          success: false,
          message: `Only ${product.stockQuantity} items available in stock`
        });
      }

      // Find or create cart
      let cart = await Cart.findOne({ user: userId });
      if (!cart) {
        cart = new Cart({ user: userId, items: [], totalAmount: 0 });
      }

      // Check if item already exists in cart (same product, size, color)
      const existingItemIndex = cart.items.findIndex(item => 
        item.product.toString() === productId &&
        item.size === size &&
        item.color === color
      );

      if (existingItemIndex > -1) {
        // Update quantity of existing item
        const newQuantity = cart.items[existingItemIndex].quantity + quantity;
        
        // Check total stock availability
        if (product.stockQuantity < newQuantity) {
          return res.status(400).json({
            success: false,
            message: `Cannot add ${quantity} more items. Only ${product.stockQuantity - cart.items[existingItemIndex].quantity} items available`
          });
        }

        cart.items[existingItemIndex].quantity = newQuantity;
      } else {
        // Add new item to cart
        cart.items.push({
          product: new Types.ObjectId(productId),
          quantity,
          size,
          color,
          priceAtTime: product.price
        });
      }

      await cart.save();

      // Populate product details for response
      await cart.populate({
        path: 'items.product',
        select: 'productName productMedia price sizes colors stockQuantity'
      });

      res.status(200).json({
        success: true,
        message: 'Item added to cart successfully',
        data: cart
      });

    } catch (error: any) {
      console.error('Add to cart error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to add item to cart',
        error: error.message
      });
    }
  };

export const updateCartItem = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;
      const { productId, size, color } = req.params;
      const { quantity }: IUpdateCartItemRequest = req.body;

      if (!quantity || quantity < 0) {
        return res.status(400).json({
          success: false,
          message: 'Valid quantity is required'
        });
      }

      const cart = await Cart.findOne({ user: userId });
      if (!cart) {
        return res.status(404).json({
          success: false,
          message: 'Cart not found'
        });
      }

      // Find the item in cart
      const itemIndex = cart.items.findIndex(item => 
        item.product.toString() === productId &&
        item.size === size &&
        item.color === color
      );

      if (itemIndex === -1) {
        return res.status(404).json({
          success: false,
          message: 'Item not found in cart'
        });
      }

      if (quantity === 0) {
        // Remove item if quantity is 0
        cart.items.splice(itemIndex, 1);
      } else {
        // Check stock availability
        const product = await Product.findById(productId);
        if (!product || product.stockQuantity < quantity) {
          return res.status(400).json({
            success: false,
            message: `Only ${product?.stockQuantity || 0} items available in stock`
          });
        }

        // Update quantity
        cart.items[itemIndex].quantity = quantity;
      }

      await cart.save();

      // Populate product details for response
      await cart.populate({
        path: 'items.product',
        select: 'productName productMedia price sizes colors stockQuantity'
      });

      res.status(200).json({
        success: true,
        message: 'Cart updated successfully',
        data: cart
      });

    } catch (error: any) {
      console.error('Update cart item error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update cart item',
        error: error.message
      });
    }
  };

  export const removeFromCart = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;
      const { productId, size, color } = req.params;

      const cart = await Cart.findOne({ user: userId });
      if (!cart) {
        return res.status(404).json({
          success: false,
          message: 'Cart not found'
        });
      }

      // Find and remove the item
      const itemIndex = cart.items.findIndex(item => 
        item.product.toString() === productId &&
        item.size === size &&
        item.color === color
      );

      if (itemIndex === -1) {
        return res.status(404).json({
          success: false,
          message: 'Item not found in cart'
        });
      }

      cart.items.splice(itemIndex, 1);
      await cart.save();

      // Populate product details for response
      await cart.populate({
        path: 'items.product',
        select: 'productName productMedia price sizes colors stockQuantity'
      });

      res.status(200).json({
        success: true,
        message: 'Item removed from cart successfully',
        data: cart
      });

    } catch (error: any) {
      console.error('Remove from cart error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to remove item from cart',
        error: error.message
      });
    }
  };

export const clearCart = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;

      const cart = await Cart.findOne({ user: userId });
      if (!cart) {
        return res.status(404).json({
          success: false,
          message: 'Cart not found'
        });
      }

      cart.items = [];
      cart.totalAmount = 0;
      cart.appliedPromoCode = undefined;
      cart.discountAmount = 0;
      await cart.save();

      res.status(200).json({
        success: true,
        message: 'Cart cleared successfully',
        data: cart
      });

    } catch (error: any) {
      console.error('Clear cart error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to clear cart',
        error: error.message
      });
    }
  };

  export const getCartItemCount = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;

      const cart = await Cart.findOne({ user: userId });
      const itemCount = cart ? cart.items.reduce((total, item) => total + item.quantity, 0) : 0;

      res.status(200).json({
        success: true,
        message: 'Cart item count retrieved successfully',
        data: { itemCount }
      });

    } catch (error: any) {
      console.error('Get cart item count error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get cart item count',
        error: error.message
      });
    }
  };