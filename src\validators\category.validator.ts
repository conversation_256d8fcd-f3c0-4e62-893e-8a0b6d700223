// expressValidation.ts
import { body, param, query, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import Category from '../models/Category';

// Validate create category input
export const validateCreateCategory = [
  body('categoryName')
    .trim()
    .notEmpty()
    .withMessage('Category name is required')
    .isLength({ min: 1, max: 100 })
    .withMessage('Category name must be between 1 and 100 characters'),
  body('categoryDescription')
    .trim()
    .notEmpty()
    .withMessage('Category description is required')
    .isLength({ max: 500 })
    .withMessage('Category description cannot exceed 500 characters'),
  body('categoryType')
    .trim()
    .notEmpty()
    .withMessage('Category type is required')
    .isIn(['electronics', 'clothing', 'home', 'books', 'sports', 'beauty', 'automotive', 'toys', 'food', 'other']) // Assuming the types are 'physical' or 'digital', adjust as needed
    .withMessage('Category type must be one of: electronics, clothing, home, books, sports, beauty, automotive, toys, food, other'),

  // Custom validation to check for validation errors
  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        errors: errors.array()
      });
    }
    next();
  }
];

// Validate get all categories query parameters
export const validateGetAllCategories = [
  query('categoryType')
    .optional()
    .isString()
    .withMessage('Category type must be a string')
    .isIn(['electronics', 'clothing', 'home', 'books', 'sports', 'beauty', 'automotive', 'toys', 'food', 'other']) // Assuming the types are 'physical' or 'digital', adjust as needed
    .withMessage('Category type must be one of: electronics, clothing, home, books, sports, beauty, automotive, toys, food, other'),

  // Custom validation to check for validation errors
  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        errors: errors.array()
      });
    }
    next();
  }
];

// Validate categoryId in params for get, update, and delete
export const validateCategoryId = [
  param('categoryId')
    .isMongoId()
    .withMessage('Invalid category ID format')
    .custom(async (categoryId, { req }) => {
      const category = await Category.findById(categoryId);
      if (!category) {
        return Promise.reject('Category not found');
      }
      return true;
    }),

  // Custom validation to check for validation errors
  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        errors: errors.array()
      });
    }
    next();
  }
];

// Validate update category input
export const validateUpdateCategory = [
  body('categoryName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Category name must be between 1 and 100 characters'),
  body('categoryDescription')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Category description cannot exceed 500 characters'),
  body('categoryType')
    .optional()
    .trim()
    .isIn(['physical', 'digital'])
    .withMessage('Category type must be either "physical" or "digital"'),

  // Custom validation to check for validation errors
  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        errors: errors.array()
      });
    }
    next();
  }
];
