import { Request, Response } from 'express';
import User from '../models/User';
import DeliveryAddress from '../models/DeliveryAddress';

export const getSavedAddresses = async (req: Request, res: Response) =>{
    try {
      const userId = req.user!._id;

      const addresses = await DeliveryAddress.find({ user: userId })
        .populate('user', 'fullname')
        .sort({ isDefault: -1, createdAt: -1 });

      // Format response to include fullName from populated user
      const formattedAddresses = addresses.map(address => ({
        _id: address._id,
        fullName: (address.user as any)?.fullname || 'N/A',
        streetAddress: address.streetAddress,
        apartment: address.apartment,
        state: address.state,
        city: address.city,
        postalCode: address.postalCode,
        isDefault: address.isDefault,
        createdAt: address.createdAt,
        updatedAt: address.updatedAt
      }));

      res.status(200).json({
        success: true,
        message: 'Saved addresses retrieved successfully',
        data: formattedAddresses
      });

    } catch (error: any) {
      console.error('Get saved addresses error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve saved addresses',
        error: error.message
      });
    }
};

export const addAddress = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;
      const { streetAddress, apartment, state, city, postalCode, isDefault } = req.body;

      // Validate required fields
      if (!streetAddress || !state || !city || !postalCode) {
        return res.status(400).json({
          success: false,
          message: 'Street address, state, city, and postal code are required'
        });
      }

      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Check if this should be the default address
      const addressCount = await DeliveryAddress.countDocuments({ user: userId });
      const shouldBeDefault = isDefault || addressCount === 0;

      // Create new address
      const newAddress = new DeliveryAddress({
        user: userId,
        streetAddress,
        apartment,
        state,
        city,
        postalCode,
        isDefault: shouldBeDefault
      });

      await newAddress.save();

      // Return formatted response with user's fullname
      const formattedAddress = {
        _id: newAddress._id,
        fullName: user.fullname,
        streetAddress: newAddress.streetAddress,
        apartment: newAddress.apartment,
        state: newAddress.state,
        city: newAddress.city,
        postalCode: newAddress.postalCode,
        isDefault: newAddress.isDefault,
        createdAt: newAddress.createdAt,
        updatedAt: newAddress.updatedAt
      };

      res.status(201).json({
        success: true,
        message: 'Address added successfully',
        data: formattedAddress
      });

    } catch (error: any) {
      console.error('Add address error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to add address',
        error: error.message
      });
    }
};

export const updateAddress = async(req: Request, res: Response) => {
    try {
      const userId = req.user!._id;
      const { addressId } = req.params;
      const { streetAddress, apartment, state, city, postalCode, isDefault } = req.body;

      // Validate required fields
      if (!streetAddress || !state || !city || !postalCode) {
        return res.status(400).json({
          success: false,
          message: 'Street address, state, city, and postal code are required'
        });
      }

      // Find the address and verify ownership
      const address = await DeliveryAddress.findOne({ _id: addressId, user: userId });
      if (!address) {
        return res.status(404).json({
          success: false,
          message: 'Address not found or not owned by user'
        });
      }

      // Update address fields
      address.streetAddress = streetAddress;
      address.apartment = apartment;
      address.state = state;
      address.city = city;
      address.postalCode = postalCode;

      // Handle default address logic
      if (isDefault && !address.isDefault) {
        // Remove default from other addresses
        await DeliveryAddress.updateMany(
          { user: userId, _id: { $ne: addressId } },
          { isDefault: false }
        );
        address.isDefault = true;
      } else if (isDefault === false && address.isDefault) {
        // If removing default status, check if there are other addresses
        const otherAddresses = await DeliveryAddress.find({ user: userId, _id: { $ne: addressId } });
        if (otherAddresses.length > 0) {
          address.isDefault = false;
          // Make the first other address default
          await DeliveryAddress.findByIdAndUpdate(otherAddresses[0]._id, { isDefault: true });
        } else {
          // Keep as default if it's the only address
          address.isDefault = true;
        }
      }

      await address.save();

      // Get user info for response
      const user = await User.findById(userId);

      const formattedAddress = {
        _id: address._id,
        fullName: user?.fullname || 'N/A',
        streetAddress: address.streetAddress,
        apartment: address.apartment,
        state: address.state,
        city: address.city,
        postalCode: address.postalCode,
        isDefault: address.isDefault,
        createdAt: address.createdAt,
        updatedAt: address.updatedAt
      };

      res.status(200).json({
        success: true,
        message: 'Address updated successfully',
        data: formattedAddress
      });

    } catch (error: any) {
      console.error('Update address error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update address',
        error: error.message
      });
    }
};

export const  deleteAddress = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;
      const { addressId } = req.params;

      // Find the address and verify ownership
      const address = await DeliveryAddress.findOne({ _id: addressId, user: userId });
      if (!address) {
        return res.status(404).json({
          success: false,
          message: 'Address not found or not owned by user'
        });
      }

      const wasDefault = address.isDefault;

      // Delete the address
      await DeliveryAddress.findByIdAndDelete(addressId);

      // If this was the default address, make another address default
      if (wasDefault) {
        const remainingAddress = await DeliveryAddress.findOne({ user: userId });
        if (remainingAddress) {
          remainingAddress.isDefault = true;
          await remainingAddress.save();
        }
      }

      res.status(200).json({
        success: true,
        message: 'Address deleted successfully'
      });

    } catch (error: any) {
      console.error('Delete address error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete address',
        error: error.message
      });
    }
};

export const setDefaultAddress = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;
      const { addressId } = req.params;

      // Find the address and verify ownership
      const address = await DeliveryAddress.findOne({ _id: addressId, user: userId });
      if (!address) {
        return res.status(404).json({
          success: false,
          message: 'Address not found or not owned by user'
        });
      }

      // Remove default from all other addresses
      await DeliveryAddress.updateMany(
        { user: userId, _id: { $ne: addressId } },
        { isDefault: false }
      );

      // Set this address as default
      address.isDefault = true;
      await address.save();

      res.status(200).json({
        success: true,
        message: 'Default address updated successfully'
      });

    } catch (error: any) {
      console.error('Set default address error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to set default address',
        error: error.message
      });
    }
};

export const getAddressById = async (req: Request, res: Response) => {
    try {
      const userId = req.user!._id;
      const { addressId } = req.params;

      const address = await DeliveryAddress.findOne({ _id: addressId, user: userId })
        .populate('user', 'fullname');

      if (!address) {
        return res.status(404).json({
          success: false,
          message: 'Address not found or not owned by user'
        });
      }

      const formattedAddress = {
        _id: address._id,
        fullName: (address.user as any)?.fullname || 'N/A',
        streetAddress: address.streetAddress,
        apartment: address.apartment,
        state: address.state,
        city: address.city,
        postalCode: address.postalCode,
        isDefault: address.isDefault,
        createdAt: address.createdAt,
        updatedAt: address.updatedAt
      };

      res.status(200).json({
        success: true,
        message: 'Address retrieved successfully',
        data: formattedAddress
      });

    } catch (error: any) {
      console.error('Get address by ID error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve address',
        error: error.message
      });
    }
  };